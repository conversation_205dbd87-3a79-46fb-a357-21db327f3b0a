﻿using ContinuityPatrol.CrossCuttingConcerns.Helper;
using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.ExceptionHandler;

namespace ContinuityPatrol.Workflow.Engine;

public class MonitorService
{
    private readonly ILogger _logger;
    private readonly IDataProvider _dataProvider;
    private readonly Job _currentJob;
    private readonly ExceptionManager? _alertManager;
    private readonly MonitorServiceFactory _monitorServiceFactory;
    private readonly DashboardUpdater _dashboardUpdater;

    public MonitorService(ILogger logger, IDataProvider dataProvider, Job currentJob)
    {
        _logger = logger;
        _dataProvider = dataProvider;
        _currentJob = currentJob;
        _alertManager = new ExceptionManager(dataProvider, logger);
        _monitorServiceFactory = new MonitorServiceFactory(dataProvider, logger);
        _dashboardUpdater = new DashboardUpdater(dataProvider, logger);
    }

    public async Task HandleStatusUpdatesAndLogging(string actionResultStatus, string errorMessage, string infraId)
    {
        try
        {
            _logger.Debug("Entered into HandleStatusUpdatesAndLogging method");

            await PerformDatabaseActions(actionResultStatus, infraId, errorMessage);
            await _dashboardUpdater.PerformDashBoardActions(_logger);

            _logger.Debug("HandleStatusUpdatesAndLogging method ended.");
        }
        catch (Exception exc)
        {
            _logger.Error("Exception occurring in HandleStatusUpdatesAndLogging " + exc.Message);
            throw;
        }
    }

    private async Task PerformDatabaseActions(string actionResultStatus, string infraId, string errorMessage)
    {
        try
        {
            _logger.Information("Entered into PerformDatabaseActions");

            if (!actionResultStatus.Contains("error", StringComparison.CurrentCultureIgnoreCase) && !string.IsNullOrWhiteSpace(actionResultStatus))
            {
                var decryptValue = SecurityHelper.Decrypt(actionResultStatus);
                var value = JObject.Parse(decryptValue);
                var dataLag = FindDataLag(value);
                dynamic deserialize = JsonConvert.DeserializeObject(decryptValue)!;

                string type = deserialize.SelectToken("Monitor_Type");
                var infraObject = await _dataProvider.InfraObject.GetInfraObjectById(infraId);
                infraObject.ReplicationStatus = 2;
                await _dataProvider.InfraObject.UpdateInfraObject(infraObject);

                MonitorServiceFactory monitorServiceFactory = new(_dataProvider, _logger);
                await monitorServiceFactory.UpdateMonitorServiceLogStatus(_currentJob, actionResultStatus, type, dataLag!, infraObject.ReferenceId, errorMessage);
            }
            else
            {
                var infraObject = await _dataProvider.InfraObject.GetInfraObjectById(infraId);
                infraObject.ReplicationStatus = 3;
                await _dataProvider.InfraObject.UpdateInfraObject(infraObject);

                var job = await _dataProvider.Job.GetJobById(_currentJob.ReferenceId);
                job.Status = "Error";
                job.ExceptionMessage = errorMessage;
                await _dataProvider.Job.UpdateJobById(job);

                var dashboardView = await _dataProvider.DashboardView.GetDashboardViewByInfraId(infraId);
                dashboardView.ReplicationStatus = 3;
                await _dataProvider.DashboardView.UpdateDashboardViewById(dashboardView);
                _logger.Error("actionResultStatus monitor output is Error ");
            }
            _logger.Information("PerformDatabaseActions method ended");
        }
        catch (Exception exc)
        {
            _logger.Error("Exception occurring in PerformDatabaseActions " + exc.Message);
        }
    }

    public static string FindDataLag(JToken token)
    {
        Queue<JToken> tokensToCheck = new();
        tokensToCheck.Enqueue(token);

        while (tokensToCheck.Count > 0)
        {
            var currentToken = tokensToCheck.Dequeue();
            if (currentToken.Type == JTokenType.Object)
            {
                foreach (var property in currentToken.Children<JProperty>())
                {
                    if (property.Name.Equals("PR_Datalag", StringComparison.OrdinalIgnoreCase) || property.Name.Equals("datalag",StringComparison.OrdinalIgnoreCase))
                    {
                        return property.Value.ToString();
                    }

                    tokensToCheck.Enqueue(property.Value);
                }
            }
            else if (currentToken.Type == JTokenType.Array)
            {
                foreach (var item in currentToken.Children())
                {
                    tokensToCheck.Enqueue(item);
                }
            }
        }

        return "NA";
    }

    public MonitorServiceStatus MapToMonitorServiceStatus<T>(T monitorStatus) where T : class
    {
        try
        {
            if (monitorStatus is null) throw new ArgumentNullException(nameof(monitorStatus));

            var result = new MonitorServiceStatus();
            Type sourceType = monitorStatus.GetType();
            Type destinationType = typeof(MonitorServiceStatus);

            PropertyInfo[] sourceProperties = sourceType.GetProperties();
            PropertyInfo[] destinationProperties = destinationType.GetProperties();

            foreach (var sourceProperty in sourceProperties)
            {
                PropertyInfo destinationProperty = destinationProperties.FirstOrDefault(p => p.Name == sourceProperty.Name && p.PropertyType == sourceProperty.PropertyType)!;
                if (destinationProperty != null && destinationProperty.CanWrite) destinationProperty.SetValue(result, sourceProperty.GetValue(monitorStatus));
            }

            return result;
        }
        catch (Exception exc)
        {
            _logger.Error("Exception occurring in MapToMonitorServiceStatus method" + exc.Message);
            throw;
        }
    }
}