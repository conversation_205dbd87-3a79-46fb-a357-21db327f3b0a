using ContinuityPatrol.Core.Contract;
using ContinuityPatrol.Core.Workflow.Jobs;
using ContinuityPatrol.CrossCuttingConcerns.Extensions;
using ContinuityPatrol.CrossCuttingConcerns.Helper;
using ContinuityPatrol.CrossCuttingConcerns.Shared;
using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Contract.Persistence;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.Plugins.SecureShell;


namespace ContinuityPatrol.Core.Workflow;

public class WorkflowJobCore : IJobCore
{    // Dependencies
    private readonly ILogger _logger;
    private readonly IWorkflowActionHubService _workflowActionHubService;
    private readonly IWorkflowHubService _workflowHubService;
    private readonly IDataProvider _dataProvider;
    private readonly IServiceProvider _serviceProvider;

    // State
    private IScheduler _workflowScheduler;
    public bool Initiated { get; set; }

    // Constants
    private const string CancellationTokenSourceKey = "CancellationTokenSource";
    private const int DefaultThreadCount = 200;
    private const int DefaultMisfireThreshold = 5000;
    private const int DefaultIdleWaitTime = 30000;
    public WorkflowJobCore(ILogger logger, IServiceScopeFactory serviceScopeFactory, IWorkflowActionHubService workflowActionHubService, IWorkflowHubService workflowHubService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _workflowActionHubService = workflowActionHubService ?? throw new ArgumentNullException(nameof(workflowActionHubService));
        _workflowHubService = workflowHubService ?? throw new ArgumentNullException(nameof(workflowHubService));

        if (serviceScopeFactory == null)
            throw new ArgumentNullException(nameof(serviceScopeFactory));

        var scope = serviceScopeFactory.CreateScope();
        _dataProvider = scope.ServiceProvider.GetRequiredService<IDataProvider>() ??
                        throw new InvalidOperationException("Failed to resolve IDataProvider from service scope");
        _serviceProvider = scope.ServiceProvider;
    }
    public async Task Start()
    {
        if (_workflowScheduler == null)
        {
            _logger.Warning("Cannot start scheduler: Scheduler has not been initialized");
            throw new InvalidOperationException("Scheduler has not been initialized. Call Initialize() first.");
        }

        try
        {
            if (!_workflowScheduler.IsStarted)
            {
                await _workflowScheduler.Start();
                _logger.Information("CP Workflow scheduler started successfully");
            }
            else
            {
                _logger.Information("CP Workflow scheduler is already running");
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to start CP Workflow scheduler");
            throw;
        }
    }
    public async Task Stop()
    {
        if (_workflowScheduler == null)
        {
            _logger.Warning("Cannot stop scheduler: Scheduler has not been initialized");
            return;
        }

        try
        {
            if (!_workflowScheduler.IsShutdown)
            {
                // Wait for jobs to complete (true parameter)
                await _workflowScheduler.Shutdown(true);
                _logger.Information("CP Workflow scheduler stopped successfully");
            }
            else
            {
                _logger.Information("CP Workflow scheduler is already stopped");
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to stop CP Workflow scheduler");
            throw;
        }
    }
    public async Task Initialize()
    {
        if (Initiated)
        {
            _logger.Information("Workflow scheduler is already initialized");
            return;
        }

        try
        {
            _logger.Information("Initializing Quartz Scheduler instance");

            await CreateScheduler();
            await _workflowScheduler.Start();

            _logger.Information("Starting SignalR hub connections");
            await _workflowActionHubService.StartConnectionAsync();
            await _workflowHubService.StartConnectionAsync();

            Initiated = true;
            _logger.Information("Quartz Scheduler instance initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Quartz Scheduler initialization failed");
            throw;
        }
    }
    public async Task PauseWorkflow(string workflowOperationGroupId)
    {
        ValidateStringParameter(workflowOperationGroupId, nameof(workflowOperationGroupId),true);

        if (_workflowScheduler == null)
        {
            _logger.Error("Cannot pause workflow: Scheduler has not been initialized");
            throw new InvalidOperationException("Scheduler has not been initialized. Call Initialize() first.");
        }

        try
        {
            _logger.Debug("Pausing workflow operation for group ID: {GroupId}", workflowOperationGroupId);

            var operationGroup = await GetWorkflowOperationGroup(workflowOperationGroupId);
            if (operationGroup == null) return;

            if (operationGroup.IsPause != 1)
            {
                _logger.Information("Pause not requested (IsPause != 1) for workflow operation group: {GroupId}",
                    workflowOperationGroupId);
                return;
            }

            var jobKey = CreateJobKey(operationGroup);
            await PauseAndDeleteJob(jobKey);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error occurred while executing PauseWorkflow for group ID: {GroupId}",
                workflowOperationGroupId);
            throw;
        }
    }
    public async Task<string> ExecuteWorkflow(string workflowOperationId, string groupIds)
    {
        try
        {
            ValidateStringParameter(workflowOperationId, nameof(workflowOperationId),true);
            ValidateStringParameter(groupIds, nameof(groupIds));

            await EnsureSchedulerIsReady();
            _logger.Information("Executing workflow operation: {OperationId} with group IDs: {GroupIds}",
                workflowOperationId, groupIds);
            var groupIdList = ParseGroupIds(groupIds);
            if (!groupIdList.Any())
            {
                _logger.Error("No valid group IDs provided for workflow operation: {OperationId}", workflowOperationId);
                return null;
            }
            // Get and validate workflow operation
            var workflowOperation = await GetWorkflowOperation(workflowOperationId);
            if (workflowOperation == null)
            {
                return null;
            }
            var operationGroups = await GetOperationGroups(workflowOperationId, groupIdList);
            if (!operationGroups.Any())
            {
                _logger.Error("No valid operation groups found for workflow operation: {OperationId}", workflowOperationId);
                return null;
            }
            // Validate infra objects
            if (!await ValidateInfraObjects(operationGroups, workflowOperationId))
            {
                return null;
            }

            // Update workflow operation status
            await UpdateWorkflowStatus(workflowOperation, OperationStatus.RUNNING);

            // Schedule workflow jobs
            await ScheduleWorkflowJobs(operationGroups, workflowOperation);

            return workflowOperation.ProfileName;


        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error executing workflow operation: {OperationId}", workflowOperationId);
            throw;
        }
    }
    public async ValueTask DisposeAsync()
    {
        if (_workflowScheduler is not null)
        {
            await _workflowScheduler.Shutdown(false);
        }
    }
    public async Task TestServer(string serverId)
    {
        ValidateStringParameter(serverId, nameof(serverId),true);

        try
        {
            _logger.Information("{ServerId}: Executing Test Server Connection...", serverId);

            var serverList = await _dataProvider.Server.GetServerById(serverId);
            if (serverList == null)
            {
                _logger.Error("{ServerId}: Server not found", serverId);
                return;
            }

            var client = (ISshClient)_serviceProvider.GetService(typeof(ISshClient))!;
            if (client == null)
            {
                _logger.Error("{ServerId}: Failed to resolve ISshClient from service provider", serverId);
                return;
            }

            var actionResult = await client.Connect(serverList);
            serverList.Status = actionResult.IsSuccess ? "UP" : "DOWN";

            _logger.Information("{ServerId}: Server Status... {Status}", serverId, serverList.Status);

            await _dataProvider.Server.UpdateServer(serverList);
        }
        catch (Exception exc)
        {
            _logger.Error(exc, "{ServerId}: Test server Connection Failed", serverId);
        }
    }
    private async Task ScheduleWorkflowJobs(IReadOnlyList<WorkflowOperationGroup> operationGroups, WorkflowOperation workflowOperation)
    {
        _logger.Information("Scheduling {Count} workflow operation groups for profile: {ProfileName}",
            operationGroups.Count, workflowOperation.ProfileName);

        var scheduleTasks = new List<Task>();
        var successCount = 0;

        foreach (var group in operationGroups)
        {
            try
            {
                var task = ScheduleWorkflowJob(group, workflowOperation.ProfileName);
                scheduleTasks.Add(task);
                successCount++;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to schedule workflow job for group: {GroupId}, operation: {OperationId}",
                    group.Id, workflowOperation.Id);
                // Continue with other groups even if one fails
            }
        }

        if (scheduleTasks.Count == 0)
        {
            _logger.Error("No workflow jobs could be scheduled for operation: {OperationId}", workflowOperation.Id);
            throw new InvalidOperationException($"Failed to schedule any jobs for workflow operation: {workflowOperation.Id}");
        }

        await Task.WhenAll(scheduleTasks);

        _logger.Information("Successfully scheduled {ScheduledCount} of {TotalCount} jobs for profile: {ProfileName}",
            successCount, operationGroups.Count, workflowOperation.ProfileName);
    }
    private async Task UpdateWorkflowStatus(WorkflowOperation workflowOperation, OperationStatus status)
    {
        workflowOperation.Status = status.ToTitleCase();
        _logger.Information("Updating workflow operation status to '{Status}' for ID: {OperationId}",
            workflowOperation.Status, workflowOperation.Id);

        await _dataProvider.WorkflowOperation.UpdateByIdDrOperation(workflowOperation);
    }
    private async Task<bool> ValidateInfraObjects(IReadOnlyList<WorkflowOperationGroup> operationGroups, string workflowOperationId)
    {
        foreach (var group in operationGroups)
        {
            if (string.IsNullOrEmpty(group.InfraObjectId))
            {
                _logger.Warning("Operation group {GroupId} has no associated infra object ID", group.Id);
                continue;
            }

            var infraObject = await _dataProvider.InfraObject.GetInfraObjectById(group.InfraObjectId);

            if (infraObject == null)
            {
                _logger.Error("Infra object not found for ID: {InfraObjectId}, workflow operation ID: {OperationId}",
                    group.InfraObjectId, workflowOperationId);
                continue;
            }

            if (string.IsNullOrEmpty(infraObject.State))
            {
                _logger.Warning("Infra object {InfraObjectId} has null or empty state", group.InfraObjectId);
                continue;
            }

            if (!infraObject.State.Equals("locked", StringComparison.OrdinalIgnoreCase)) continue;
            var errorMessage = $"License is expired && InfraObject State: {infraObject.State}";
            _logger.Error(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        return true;
    }
    private async Task<IReadOnlyList<WorkflowOperationGroup>> GetOperationGroups(string workflowOperationId, IReadOnlyList<string> groupIdList)
    {
        var workflowOperationGroupList = await _dataProvider.WorkflowOperationGroup
            .GetWorkflowOperationGroupsByOperationId(workflowOperationId);

        if (workflowOperationGroupList == null || !workflowOperationGroupList.Any())
        {
            _logger.Error("No workflow operation groups found for operation ID: {OperationId}",
                workflowOperationId);
            return Array.Empty<WorkflowOperationGroup>();
        }

        var operationGroups = workflowOperationGroupList
            .Where(g => g != null && groupIdList.Contains(g.ReferenceId))
            .ToList();

        if (!operationGroups.Any())
        {
            _logger.Error("No matching operation groups found for the provided group IDs. Operation ID: {OperationId}",
                workflowOperationId);
            return Array.Empty<WorkflowOperationGroup>();
        }

        _logger.Information("Found {Count} matching operation groups for workflow operation: {OperationId}",
            operationGroups.Count, workflowOperationId);

        return operationGroups;
    }
    private async Task EnsureSchedulerIsReady()
    {
        if (_workflowScheduler == null)
        {
            _logger.Error("Cannot execute workflow: Scheduler has not been initialized");
            throw new InvalidOperationException("Scheduler has not been initialized. Call Initialize() first.");
        }

        if (!_workflowScheduler.IsStarted)
        {
            _logger.Warning("Workflow scheduler is not started. Starting scheduler now.");
            await _workflowScheduler.Start();
        }
    }
    private IReadOnlyList<string> ParseGroupIds(string groupIds)
    {
        var result = groupIds
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(id => id.Trim())
            .Where(id => !string.IsNullOrEmpty(id))
            .ToList();

        _logger.Debug("Parsed {Count} group IDs", result.Count);
        return result;
    }
    private async Task<WorkflowOperation> GetWorkflowOperation(string workflowOperationId)
    {
        var workflowOperation = await _dataProvider.WorkflowOperation.GetWorkflowOperationById(workflowOperationId);

        if (workflowOperation != null) return workflowOperation;
        _logger.Error("Workflow operation not found for ID: {OperationId}", workflowOperationId);
        return null;
    }
    private async Task<WorkflowOperationGroup> GetWorkflowOperationGroup(string workflowOperationGroupId)
    {
        var operationGroup = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(workflowOperationGroupId);
        if (operationGroup != null) return operationGroup;
        _logger.Error("No workflow operation group found for ID: {GroupId}", workflowOperationGroupId);
        throw new KeyNotFoundException($"No workflow operation group found for ID: {workflowOperationGroupId}");

    }
    private async Task ScheduleWorkflowJob(WorkflowOperationGroup group, string profileName)
    {
        var logger = Log.ForContext("FileName", $"{profileName}___{group.WorkflowName}-{DateTime.Now:HHmm}").ForContext("GroupId", group.ReferenceId);

        logger.Information($"{group.WorkflowName} : Scheduling workflow Job ...");

        var jobName = $"{group.WorkflowName}_{group.Id}_ParallelJob_{group.JobName}";

        logger.Debug($"{group.WorkflowName} : Creating workflow job name: {jobName} ...");

        var groupName = $"{group.WorkflowName}_{group.Id}_ParallelJobGroup_{group.JobName}";

        var workflowJobDetails = JobBuilder.Create<WorkflowJob>()
            .WithIdentity(jobName, groupName)
            .StoreDurably()
            .Build();
        workflowJobDetails.JobDataMap[WorkflowJob.GroupWorkflow] = group;

        workflowJobDetails.JobDataMap[WorkflowJob.GroupWorkflowLogger] = logger;

        workflowJobDetails.JobDataMap[WorkflowJob.ServiceProvider] = _serviceProvider;

        workflowJobDetails.JobDataMap[WorkflowJob.DataProvider] = _dataProvider;

        var triggerGroupName = $"{group.WorkflowName}_{group.Id}_ParallelTriggerGroup";

        var trigger = TriggerBuilder.Create()
            .WithIdentity(triggerGroupName)
            .StartAt(DateTimeOffset.UtcNow)
            .WithSimpleSchedule(x => x
                .WithRepeatCount(0)
                .WithMisfireHandlingInstructionFireNow())
            .Build();

        await _workflowScheduler.Start();
        await _workflowScheduler.ScheduleJob(workflowJobDetails, trigger);

        logger.Information($"{group.WorkflowName} : Workflow Job '{jobName}' scheduled successfully");
    }
    public async Task ResumeWorkflow(string workflowOperationGroupId)
    {
        try
        {
            _logger.Information($"{workflowOperationGroupId} : Resuming Workflow operation Id ...");

            await PerformResumeWorkflow(workflowOperationGroupId);
        }
        catch (Exception exc)
        {
            _logger.Error($"{workflowOperationGroupId} : Resume Workflow operation Failed. {exc.Message}");
        }
    }
    private async Task CreateScheduler()
    {
        try
        {
            _logger.Information("Creating Quartz scheduler with optimized configuration");

            var properties = CreateSchedulerProperties();
            var factory = new StdSchedulerFactory(properties);
            _workflowScheduler = await factory.GetScheduler();

            // Clear any existing jobs
            await _workflowScheduler.Clear();

            _logger.Information("Quartz scheduler created successfully");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to create Quartz scheduler");
            throw;
        }
    }
    private NameValueCollection CreateSchedulerProperties()
    {
        return new NameValueCollection {
            // Basic scheduler configuration
            { "quartz.scheduler.instanceName", "CP_Workflow_Scheduler" },
            { "quartz.scheduler.instanceId", "AUTO" },
            
            // Use RAM job store for better performance
            { "quartz.jobStore.type", "Quartz.Simpl.RAMJobStore, Quartz" },
            
            // Thread pool configuration
            { "quartz.threadPool.type", "Quartz.Simpl.SimpleThreadPool, Quartz" },
            { "quartz.threadPool.threadCount", DefaultThreadCount.ToString() },
            { "quartz.threadPool.threadPriority", "Normal" },
            
            // Performance settings
            { "quartz.jobStore.misfireThreshold", DefaultMisfireThreshold.ToString() },
            { "quartz.scheduler.idleWaitTime", DefaultIdleWaitTime.ToString() },
            
            // Enable interruption for job cancellation
            { "quartz.scheduler.interruptJobsOnShutdown", "true" },
            { "quartz.scheduler.interruptJobsOnShutdownWithWait", "true" }
        };
    }
    private async Task PerformResumeWorkflow(string workflowOperationGroupId)
    {
        _logger.Debug($"{workflowOperationGroupId} : Loading Resume Workflow Operation GroupId");

        var operationGroups = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(workflowOperationGroupId);

        if (operationGroups != null)
        {
            var wfOperation = await _dataProvider.WorkflowOperation.GetWorkflowOperationById(operationGroups.WorkflowOperationId);

            _logger.Information($"{workflowOperationGroupId}");

            await ScheduleWorkflowJob(operationGroups, wfOperation.ProfileName);
        }
        else
        {
            _logger.Error($"{workflowOperationGroupId} : No Workflow Operation Groups Found for execute.");
        }
    }
    private async Task PauseAndDeleteJob(JobKey jobKey)
    {
        var currentJobs = await _workflowScheduler.GetCurrentlyExecutingJobs();

        _logger.Debug("Found {Count} currently executing jobs", currentJobs.Count);

        var jobFound = false;

        foreach (var executionContext in currentJobs)
        {
            if (!jobKey.Equals(executionContext.JobDetail.Key)) continue;
            jobFound = true;
            _logger.Debug("Found matching job to pause: {JobKey}", executionContext.JobDetail.Key);

            var data = executionContext.JobDetail.JobDataMap;
            var cancellationTokenSource = data.Get(CancellationTokenSourceKey) as CancellationTokenSource;

            if (cancellationTokenSource == null)
            {
                _logger.Warning("CancellationTokenSource not found in job data map for job: {JobKey}", jobKey);
                continue;
            }

            // Cancel the job
            await cancellationTokenSource.CancelAsync();

            try
            {
                // Interrupt the job
                await _workflowScheduler.Interrupt(jobKey, cancellationTokenSource.Token);
                _logger.Debug("Job interrupted: {JobKey}", jobKey);

                // Pause the job
                await _workflowScheduler.PauseJob(jobKey, cancellationTokenSource.Token);
                _logger.Debug("Job paused: {JobKey}", jobKey);

                // Delete the job
                var isDeleted = await _workflowScheduler.DeleteJob(jobKey, cancellationTokenSource.Token);
                _logger.Information("Job {JobKey} deletion {Status}",
                    jobKey, isDeleted ? "succeeded" : "failed");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error while interrupting/pausing/deleting job: {JobKey}", jobKey);
            }
        }

        if (!jobFound)
        {
            _logger.Warning("No matching job found to pause for job key: {JobKey}", jobKey);
        }
    }
    private JobKey CreateJobKey(WorkflowOperationGroup group)
    {
        var jobName = $"{group.WorkflowName}_{group.Id}_ParallelJob_{group.JobName}";
        var groupName = $"{group.WorkflowName}_{group.Id}_ParallelJobGroup_{group.JobName}";

        return new JobKey(jobName, groupName);
    }
    private void ValidateStringParameter(string parameter, string parameterName,bool guidValidation=false)
    {
        if (parameter == null)
        {
            _logger.Error("Parameter {ParameterName} is null", parameterName);
            throw new ArgumentNullException(parameterName);
        }

        if (string.IsNullOrWhiteSpace(parameter))
        {
            _logger.Error("Parameter {ParameterName} is empty or whitespace", parameterName);
            throw new ArgumentException($@"Parameter '{parameterName}' cannot be empty or whitespace", parameterName);
        }
        if (guidValidation)
        {
            Guard.Against.InvalidGuidOrEmpty(parameter, parameterName, $"Parameter {parameterName} is invalid Guid Format");
        }
    }
}