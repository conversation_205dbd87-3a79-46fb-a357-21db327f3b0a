﻿using ContinuityPatrol.CrossCuttingConcerns.Extensions;
using ContinuityPatrol.CrossCuttingConcerns.Shared;
using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.Plugins.Common;
using ContinuityPatrol.Plugins.Common.Variables;

namespace ContinuityPatrol.Workflow.Engine;

public class WorkflowClient : IClient
{
    public WorkflowOperationGroup CurrentWorkflowOperationGroup { get; set; }
    public CancellationTokenSource CancellationTokenSource { get; set; }
    public int TotalActionCount { get; set; }

    private readonly ILogger _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IServiceProvider _serviceProvider;

    public WorkflowClient(WorkflowOperationGroup workflowOperation,
        IDataProvider dataProvider,
        IServiceProvider serviceProvider,
        ILogger logger,
        CancellationTokenSource cancellationTokenSource)
    {
        CurrentWorkflowOperationGroup = workflowOperation;
        _dataProvider = dataProvider;
        _logger = logger;
        _serviceProvider = serviceProvider;
        CancellationTokenSource = cancellationTokenSource;
        TotalActionCount = 0;
    }

    public async Task<string> PerformWorkflow()
    {
        _logger.Debug($"{CurrentWorkflowOperationGroup.WorkflowName} : Verifying workflow operation properties");

        var workflowProperties = await GetWorkflowProperties();

        if (string.IsNullOrWhiteSpace(workflowProperties))
        {
            _logger.Debug($"{CurrentWorkflowOperationGroup.WorkflowName} : Workflow properties are Empty...");
            return string.Empty;
        }

        await RunWorkflow(workflowProperties);

        return string.Empty;
    }
    public async Task UpdateWorkflowOperation(WorkflowOperationGroup workflowOperationGroup, IDataProvider dataProvider, string status, string progressStatus)
    {
        var operationGroup = await dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(workflowOperationGroup.ReferenceId);
        operationGroup.Status = status;
        operationGroup.ProgressStatus = progressStatus;
        await dataProvider.WorkflowOperationGroup.UpdateWorkflowOperationGroup(operationGroup);     
        
    }
    private async Task<string> GetWorkflowProperties()
    {
        _logger.Debug($"{CurrentWorkflowOperationGroup.WorkflowName} : Customize workflow execution Enabled Flag: {CurrentWorkflowOperationGroup.IsCustom}");

        if (CurrentWorkflowOperationGroup.IsCustom)
        {
            var tempWorkflow = await _dataProvider.WorkflowExecutionTemp.GetWorkflowExecutionTempById(CurrentWorkflowOperationGroup.WorkflowExecutionTempId);
            return tempWorkflow?.Properties ?? string.Empty;
        }
        var workflow = await _dataProvider.Workflow.GetWorkflowById(CurrentWorkflowOperationGroup.WorkflowId);
        return workflow?.Properties ?? string.Empty;
    }

    private async Task RunWorkflow(string workflowProperties)
    {
        _logger.Debug($"{CurrentWorkflowOperationGroup.WorkflowName} : Binding workflow properties ...");

        var dynJsonWorkflow = JsonConvert.DeserializeObject<dynamic>(workflowProperties);

        if (dynJsonWorkflow == null)
        {
            _logger.Error("Failed to parse workflow properties.");
            return;
        }
        var isParallelExecution = workflowProperties.Contains("\"IsParallel\":true");

        var manager = WorkflowMangerFactory.GetWorkflowManger(isParallelExecution, CurrentWorkflowOperationGroup, _logger, _dataProvider, _serviceProvider, CancellationTokenSource);

        await manager.RunWorkflow(dynJsonWorkflow, ServiceType.WORKFLOW_SERVICE);

        _logger.Debug($"{CurrentWorkflowOperationGroup.WorkflowName} : Workflow actions completed.");
         

        CurrentWorkflowOperationGroup = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(CurrentWorkflowOperationGroup.ReferenceId);




        var workflowActionResultList = await _dataProvider.WorkflowActionResult
                    .GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.ReferenceId);

        _logger.Information($"***** RunWorkflow - Retrieved WorkflowActionResult to check action completed for verification: {CurrentWorkflowOperationGroup.ReferenceId}");

        // Calculate success and skip count
        var totalSuccessCount = workflowActionResultList
            .Where(x => x.Status.Equals("success", StringComparison.OrdinalIgnoreCase) ||
                        x.Status.Equals("skip", StringComparison.OrdinalIgnoreCase) ||
                        x.Status.Equals("Bypassed", StringComparison.OrdinalIgnoreCase))
            .ToList();

        _logger.Information($"**** RunWorkflow - Total successful/skipped actions: {totalSuccessCount.Count}");

        var totalcount = Convert.ToInt16(CurrentWorkflowOperationGroup.ProgressStatus.ToString().Split("/")[1]);
        _logger.Information($"**** RunWorkflow - Total Action count : {totalcount}");

        var progressStatus = $"{totalSuccessCount.Count}/"+totalcount;
        if (totalSuccessCount.Count == totalcount)
        {
            await UpdateWorkflowOperation(CurrentWorkflowOperationGroup, _dataProvider, "Completed", progressStatus);
        }

        string estimatedRto = (dynJsonWorkflow.SelectToken("estimatedrto")) ?? string.Empty;

        _logger.Information($"{CurrentWorkflowOperationGroup.WorkflowName} : Estimated RTO: {estimatedRto}");

        await Task.WhenAll(UpdateRto(estimatedRto), UpdateDrOperationStatus());
    }

    private async Task UpdateRto(string estimatedRto)
    {
        var workflowResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.ReferenceId);

        if (workflowResults.Count == 0) return;

        var firstAction = workflowResults.LastOrDefault();

        var lastAction = workflowResults.FirstOrDefault();

        if (firstAction == null || lastAction == null) return;

        var rtoDuration = CalculateDuration(firstAction.StartTime, lastAction.EndTime);

        var startWorkflowAction = workflowResults.FirstOrDefault(x => x.WorkflowActionName == "Start_WorkflowRTO");
        var stopWorkflowAction = workflowResults.LastOrDefault(x => x.WorkflowActionName == "Stop_WorkflowRTO");

        if (startWorkflowAction != null && stopWorkflowAction != null)
        {
            rtoDuration = CalculateDuration(startWorkflowAction.StartTime, stopWorkflowAction.EndTime);
        }
        _logger.Information($"{CurrentWorkflowOperationGroup.WorkflowName} : Workflow Current RTO time :" + rtoDuration);

        _logger.Information($"{CurrentWorkflowOperationGroup.WorkflowName} : Workflow Estimated RTO time :" + estimatedRto);

        await UpdateDashboardAsync(rtoDuration, estimatedRto);
    }
    private async Task UpdateDashboardAsync(string rtoDuration, string estimatedRto)
    {
        var infraObject = await _dataProvider.InfraObject.GetInfraObjectById(CurrentWorkflowOperationGroup.InfraObjectId);
        var group = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(CurrentWorkflowOperationGroup.ReferenceId);

        if (group?.Status.Equals("completed", StringComparison.OrdinalIgnoreCase) == true && infraObject != null)
        {
            var workflowInfraObject = await _dataProvider.WorkflowInfraObject.GetWorkflowByInfraIdAndWorkflowId(CurrentWorkflowOperationGroup.InfraObjectId, CurrentWorkflowOperationGroup.WorkflowId);
            var status = GetStatusFromActionType(workflowInfraObject?.ActionType);

            var dashboardView = await _dataProvider.DashboardView.GetDashboardViewByInfraId(infraObject.ReferenceId);
           
         
            if (dashboardView != null)
            {
                dashboardView.CurrentRTO = rtoDuration;
                dashboardView.EstimatedRTO = estimatedRto;
                dashboardView.DROperationStatus = status;
                dashboardView.RTOGeneratedDate = DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss");
                var dttime = DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss");
                _logger.Information("Dashboard View RTOGeneratedDate:." + dttime);
                await _dataProvider.DashboardView.UpdateDashboardViewById(dashboardView);
                _logger.Information("Updated DashboardView successfully.");
            }
        }
    }
    private async Task UpdateDrOperationStatus()
    {

        VariableClient vc = new VariableClient();
        var res = vc.GetValue("@gv@WorkflowOperationStatus");

       
        var workflowInfraObject = await _dataProvider.WorkflowInfraObject.GetWorkflowByInfraIdAndWorkflowId(CurrentWorkflowOperationGroup.InfraObjectId, CurrentWorkflowOperationGroup.WorkflowId);
        if (workflowInfraObject == null)
        {
            _logger.Error($"{CurrentWorkflowOperationGroup.InfraObjectId} : Failed to fetch workflow InfraObject for Workflow ID {CurrentWorkflowOperationGroup.WorkflowId}");
            return;
        }
        var infraObject = await _dataProvider.InfraObject.GetInfraObjectById(CurrentWorkflowOperationGroup.InfraObjectId);
        if (infraObject == null) return;

        if (!string.IsNullOrEmpty(res.ToString()))
        {
            infraObject.DROperationStatus = CurrentWorkflowOperationGroup.ActionMode.Equals(ExecutionMode.SIMULATE.ToString(), StringComparison.OrdinalIgnoreCase) ? 0 : GetStatusFromActionType(res.ToString());
        }
        else
        {
            infraObject.DROperationStatus = CurrentWorkflowOperationGroup.ActionMode.Equals(ExecutionMode.SIMULATE.ToString(), StringComparison.OrdinalIgnoreCase) ? 0 : GetStatusFromActionType(workflowInfraObject.ActionType);
        }       

        var group = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(CurrentWorkflowOperationGroup.ReferenceId);
        if (group?.Status.Equals("completed", StringComparison.OrdinalIgnoreCase) == true)
        {
            await _dataProvider.InfraObject.UpdateInfraObject(infraObject);
            _logger.Information($"Updated InfraObject {infraObject.Name} DR Operation Status Code: {infraObject.DROperationStatus}");
        }
    }
    private static int GetStatusFromActionType(string? actionType) => actionType?.ToLower() switch
    {
        "switchover" => 2,
        "switchback" => 5,
        "failover" => 8,
        "failback" => 11,
        "custom" => 14,
        "customdr" => 15,
        _ => 0
    };

    private static string CalculateDuration(DateTime start, DateTime end) => (end - start).ToString(@"hh\:mm\:ss");
}