﻿using ContinuityPatrol.CrossCuttingConcerns.Shared;
using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.Language.Parser;
using ContinuityPatrol.Language.Parser.InterpreterManager;
using Microsoft.Extensions.DependencyInjection;

namespace ContinuityPatrol.Workflow.Engine
{
    public class ParallelWorkflowManager : IWorkflowManager
    {
        public WorkflowOperationGroup CurrentWorkflowOperationGroup { get; set; }

        private bool _isDisposed;

        private readonly ILogger _logger;
        private readonly IDataProvider _dataProvider;
        private readonly IServiceProvider _serviceProvider;

        private readonly CancellationTokenSource _cancellationTokenSource;
        
        private readonly WorkflowHelper _workflowHelper;

        private readonly InterpreterManagerModule _interpreterManager;

        public Interpreter InterpreterInstance { get; private set; }

        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(500, 1000);
        public int TotalActionCount { get; set; }
        public InfraObject InfraObject { get; set; }

        private bool _checkAbortStatus = false;



        public ParallelWorkflowManager(WorkflowOperationGroup groupWorkflow, ILogger logger, IDataProvider dataProvider, IServiceProvider serviceProvider, CancellationTokenSource cancellationToken)
        {
            CurrentWorkflowOperationGroup = groupWorkflow;
            _logger = logger;
            _dataProvider = dataProvider;
            _serviceProvider = serviceProvider;
            _cancellationTokenSource = cancellationToken;
            _workflowHelper = new WorkflowHelper(CurrentWorkflowOperationGroup, _dataProvider, _logger);
            _interpreterManager = new InterpreterManagerModule(_logger);
        }
         
        public async Task<bool> RunWorkflow(dynamic dynJsonWorkflow, ServiceType type)
        {
            try
            {
                _logger.Debug("Entered into RunParallelWorkflow");

                IDictionary<string, dynamic> nodeList = new Dictionary<string, dynamic>();

                _logger.Debug("Dynamic node list: " + nodeList);

                string actionResultStatus = "";

                int totalActionCount  = _workflowHelper.GetNodeFilter(dynJsonWorkflow);

                var indexValue = await _workflowHelper.GeParallellIndexValueNew(ServiceType.WORKFLOW_SERVICE, CurrentWorkflowOperationGroup, dynJsonWorkflow);

                _interpreterManager.OnInterpreterCreated += InterpreterCreated;

                _interpreterManager.Modules = new InterpreterMangerFactory().GetModuleList(_logger, _serviceProvider, ServiceType.WORKFLOW_SERVICE, "");
 

                for (int i = indexValue; i < dynJsonWorkflow?.SelectToken("nodes").Count; i++)
                {


                    await _workflowHelper.CheckThreadState(_cancellationTokenSource);

                    if (await _workflowHelper.VerifyWorkflowAborted(_checkAbortStatus, totalActionCount))
                    {
                        break;
                    }
                    var getNodeValue = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".actionInfo");

                    if (getNodeValue == null)
                    {
                        continue;
                    }

                    var stepId = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".stepId");

                    if (stepId != null)
                    {
                        stepId = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".stepId").Value;
                    }

                    _logger.Debug("RunParallelWorkflow-Dynamic Node Value: " + getNodeValue);

                    string actionName = "";

                    if (getNodeValue.SelectToken("actionName") != null)
                    {
                        actionName = getNodeValue.SelectToken("actionName").Value;

                        await _workflowHelper.UpdateWorkflowOperationGroup(actionName);
                    }

                    bool isParallel = false;

                    if (getNodeValue.SelectToken("IsParallel") != null)
                    {
                        isParallel = getNodeValue.SelectToken("IsParallel").Value;
                    }

                    _logger.Information("To check the action is Parallel: " + isParallel);

                    var groupActions = dynJsonWorkflow.SelectToken($"nodes[{i}].groupActions");

                    if (groupActions != null)
                    {
                        _logger.Information("To check the action is Group: ");
                        int groupIndex = i;
                        for (int j = 0;
                             j < dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".groupActions").Count;
                             j++)
                        {
                            var groupActionsNode =
                                dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".groupActions")[j];
                            getNodeValue = groupActionsNode.SelectToken("actionInfo");
                            stepId = groupActionsNode.SelectToken("stepId").Value;

                            var parallelAction = new ParallelAction(CurrentWorkflowOperationGroup, _logger,
                                _dataProvider, _serviceProvider)
                            {
                                TotalActionCount = 0
                            };

                            ThreadPool.SetMinThreads(1000, 1000);

                            actionResultStatus = await parallelAction.RunParallelAction(_interpreterManager, totalActionCount, getNodeValue, stepId, false);

                            bool resultStatus = false;

                            if (actionResultStatus == "Error")
                            {
                                _logger.Information("Sequential Action Result is failed to wait for ConditionAction:" +
                                                    actionResultStatus);
                                resultStatus = false;
                            }
                            else
                            {
                                resultStatus = true;
                            }

                            var operationGroups =
                                await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(
                                    CurrentWorkflowOperationGroup.ReferenceId);
                            var WorklowActionResultList =
                                await _dataProvider.WorkflowActionResult
                                    .GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup
                                        .ReferenceId);

                            if (operationGroups.ConditionalOperation == 4)
                            {
                                var WorklowActionResult =
                                    await _dataProvider.WorkflowActionResult
                                        .GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup
                                            .ReferenceId);
                                var actionResultId = WorklowActionResult.FirstOrDefault(x => x.ConditionActionId == 4);

                                if (actionResultId != null)
                                {
                                    _logger.Information(
                                        "ABORT-ConditionalOperation 4 : Abort Button clicked for ActionResultId:" +
                                        actionResultId.ReferenceId);
                                    await _workflowHelper.WorkflowActionResultStatusUpdate("Aborted",
                                        actionResultId.ReferenceId);
                                    _logger.Information("ABORT-Updating status Abort and actionResultId: " +
                                                        actionResultId.ReferenceId);
                                    await _workflowHelper.UpdateWorkflowOperationGroupInfra("Aborted",
                                        operationGroups.ProgressStatus, operationGroups.CurrentActionName,
                                        operationGroups.CurrentActionId);
                                    _checkAbortStatus = true;
                                    _logger.Information("ABORT-Updating " + operationGroups.ProgressStatus,
                                        operationGroups.CurrentActionName,
                                        operationGroups.CurrentActionId + " in UpdateWorkflowOperationgroupInfra");

                                    resultStatus = true;
                                }
                            }

                            while (!resultStatus)
                            {
                                await Task.Delay(5000);
                                await _workflowHelper.CheckThreadState(_cancellationTokenSource);
                                operationGroups =
                                    await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(
                                        CurrentWorkflowOperationGroup.ReferenceId);
                                WorklowActionResultList =
                                    await _dataProvider.WorkflowActionResult
                                        .GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup
                                            .ReferenceId);
                                var result2 = await _workflowHelper.WaitForCondition(i, resultStatus,
                                    actionResultStatus, operationGroups, WorklowActionResultList, totalActionCount );
                                resultStatus = result2;


                                if (WorklowActionResultList.Count >= 1)
                                {
                                    var actionResultList = WorklowActionResultList.Where(x => (x.Status == "Error"))
                                        .ToList();
                                    if (actionResultList.Count >= 1)
                                    {
                                        actionResultStatus = "Error";
                                        await _workflowHelper.UpdateWorkflowOperationGroupInfra(actionResultStatus,
                                            operationGroups.ProgressStatus, operationGroups.CurrentActionName,
                                            operationGroups.CurrentActionId);
                                        //resultStatus = false;
                                    }

                                }

                                //Reload
                                if (operationGroups.ConditionalOperation == 3)
                                {
                                    _logger.Information("ConditionalOperation 3 : Reload Button clicked");
                                    var getrebindWorkflow =
                                        await _dataProvider.Workflow.GetWorkflowById(operationGroups.WorkflowId);
                                    _logger.Information("Getting workflowId from GetWorkflowById " +
                                                        operationGroups.WorkflowId);
                                    if (getrebindWorkflow != null)
                                    {
                                        dynJsonWorkflow =
                                            Newtonsoft.Json.JsonConvert.DeserializeObject(getrebindWorkflow.Properties);

                                    }
                                    else
                                    {
                                        // Handle the case when the workflow is not found
                                        // Set resultStatus to false or take appropriate action
                                    }
                                }

                                if (operationGroups.ConditionalOperation == 2)
                                {
                                    var actionResultList = WorklowActionResultList
                                        .Where(x => (x.ConditionActionId == 2) && (x.Status == "Error")).ToList();

                                    foreach (var actionResultlist in actionResultList)
                                    {
                                        if (actionResultList != null)
                                        {
                                            operationGroups =
                                                await _dataProvider.WorkflowOperationGroup
                                                    .GetWorkflowOperationGroupById(operationGroups.ReferenceId);

                                            _logger.Information(
                                                "SequentialExecutor-ConditionalOperation 2 : Retry Button clicked for ActionResultId:" +
                                                actionResultlist.ReferenceId);
                                            await _workflowHelper.WorkflowActionResultStatusUpdate("Retry",
                                                actionResultlist.ReferenceId);
                                            _logger.Information(
                                                "SequentialExecutor-ConditionalOperation 2 : Retry Button clicked for ActionResultName:" +
                                                actionResultlist.WorkflowActionName);
                                            _logger.Information(
                                                "SequentialExecutor-ConditionalOperation 2 : Retry Button clicked for ActionStepId:" +
                                                actionResultlist.StepId);


                                            _logger.Information(
                                                "SequentialExecutor-Update Status RETRY and ActionResultId in WorkflowActionResultStatusUpdate");
                                            await _dataProvider.WorkflowActionResult.DeleteWorkflowActionResultById(
                                                actionResultlist.ReferenceId);
                                            _logger.Information(
                                                "RETRY-Deleting actionResultId from DeleteWorkflowActionResultById: " +
                                                actionResultlist.ReferenceId);
                                            await _workflowHelper.UpdateWorkflowOperationGroupInfra("Running",
                                                operationGroups.ProgressStatus, operationGroups.CurrentActionName,
                                                operationGroups.CurrentActionId);
                                            _logger.Debug("Update Running " + operationGroups.ProgressStatus,
                                                operationGroups.CurrentActionName,
                                                operationGroups.CurrentActionId +
                                                " in UpdateWorkflowOperationgroupInfra");
                                            //j = j - 1;
                                            // var getNodeList = nodeList[actionResultlist.StepId];
                                            actionResultStatus =
                                                await parallelAction.RunParallelAction(_interpreterManager, totalActionCount, getNodeValue,
                                                    actionResultlist.StepId,false);

                                            _logger.Information("ConditionalOperation 3 : Retry Status:" +
                                                                actionResultStatus);

                                            if (actionResultStatus == "Success")
                                            {
                                                resultStatus = true;
                                            }
                                            else
                                            {
                                                resultStatus = false;
                                            }

                                        }
                                    }
                                }
                            }
                        }
                    }
                    else if (isParallel)
                    {
                        var checkchild = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children");

                        _logger.Debug("RunParallelWorkflow-checkchild: " + checkchild);

                        if(CurrentWorkflowOperationGroup.ConditionalOperation==(int)ConditionalOperation.TERMINATE)
                        {
                            _logger.Information("Handling TERMINATE operation...");
                              nodeList.Clear();
                            for (int j = 0; j < dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children").Count; j++)
                            {

                                var getChildNode = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children")[j];
                                var test = getChildNode.SelectToken("actionInfo");
                                stepId = getChildNode.SelectToken("stepId").Value;

                                var operationGroups = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(CurrentWorkflowOperationGroup.ReferenceId);
                                var WorklowActionResultList =
                               await _dataProvider.WorkflowActionResult
                                   .GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup
                                       .ReferenceId);
                                var failedNodesList = WorklowActionResultList
                                                        .Where(x => x.Status == "Error").ToList();
                                _logger.Information($"failedNodesList{failedNodesList.Count}");

                                if (!failedNodesList.Any()) break;

                                foreach (var failedNode in failedNodesList)
                                {
                                    try
                                    {
                                        _logger.Debug($"Retrying Terminate action for step Id {failedNode.StepId}");

                                        _logger.Information($"Retrying Terminate Workflow Service [ID:{failedNode.ReferenceId}, WorkflowActionName:{failedNode.WorkflowActionName}, StepId:{failedNode.StepId}]");
                                       
                                        if (stepId == failedNode.StepId)
                                        {
                                            await _workflowHelper.WorkflowActionResultStatusUpdate("TerminateRetry", failedNode.ReferenceId);
                                            await _dataProvider.WorkflowActionResult.DeleteWorkflowActionResultById(failedNode.ReferenceId);
                                            _logger.Information("RETRY-terminate Deleting actionResultId from DeleteWorkflowActionResultById: " + failedNode.ReferenceId);
                                            await _workflowHelper.UpdateWorkflowOperationGroupInfra(
                                           "Running",
                                           operationGroups.ProgressStatus,
                                           operationGroups.CurrentActionName,
                                           operationGroups.CurrentActionId);

                                            _logger.Information("RETRY-Deleting actionResultId from Terminate: " + failedNode.ReferenceId);

                                            _logger.Information("Terminate -StepId " + stepId);
                                            _logger.Information("Terminate -ActionResult StepId " + failedNode.StepId);
                                            nodeList.Add(failedNode.StepId, test);
                                            _logger.Information("Terminate Node List Added");

                                        }                                        

                                    }
                                    catch (Exception ex)
                                    {

                                        _logger.Error("Terminate Failed:"+ex.Message);
                                    }
                                   
                                }                                
                            }
                            _logger.Information(" TERMINATE operation Child Node Count..."+ nodeList.Count);
                        }
                        else
                        {
                            for (int j = 0; j < dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children").Count; j++)
                            {

                                var getChildNode = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children")[j];
                                var test = getChildNode.SelectToken("actionInfo");
                                stepId = getChildNode.SelectToken("stepId").Value;
                                nodeList.Add(stepId, test);
                            }

                        }

                        if (checkchild != null)
                        {
                            _logger.Debug("RunParallelWorkflow-checkchild not satisfied to null");

                            isParallel = false;

                           // int step = i;

                            if (nodeList.Count > 0)
                            {

                                try
                                {
                                    bool resultStatus = false;
                                    ThreadPool.SetMinThreads(1000, 1000);
                                   

                                    await Parallel.ForEachAsync(nodeList, new ParallelOptions { MaxDegreeOfParallelism = nodeList.Count }, async (x, token) =>
                                    {
                                        var parallelAction = new ParallelAction(CurrentWorkflowOperationGroup, _logger, _dataProvider, _serviceProvider);
                                        try
                                        {
                                            actionResultStatus = await parallelAction.RunParallelAction(_interpreterManager, totalActionCount, x.Value, x.Key, true);
                                        }
                                        catch (Exception exc)
                                        {
                                            _logger.Error("Exception occurring in RunParallelWorkflow " + exc.Message);
                                        }


                                    });

                                    if (actionResultStatus == "Error")
                                    {
                                        resultStatus = false;
                                    }
                                    else
                                    {
                                        resultStatus = true;
                                    }

                                    var operationGroups = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(CurrentWorkflowOperationGroup.ReferenceId);

                                    var WorklowActionResultList = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.ReferenceId);


                                    if (operationGroups.ConditionalOperation == 4)
                                    {
                                        var WorklowActionResult = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.ReferenceId);
                                        var actionResultId = WorklowActionResult.FirstOrDefault(x => x.ConditionActionId == 4);

                                        if (actionResultId != null)
                                        {
                                            _logger.Information("ABORT-ConditionalOperation 4 : Abort Button clicked for ActionResultId:" + actionResultId.ReferenceId);
                                            await _workflowHelper.WorkflowActionResultStatusUpdate("Aborted", actionResultId.ReferenceId);
                                            _logger.Information("ABORT-Updating status Abort and actionResultId: " + actionResultId.ReferenceId);
                                            await _workflowHelper.UpdateWorkflowOperationGroupInfra("Aborted", operationGroups.ProgressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId);
                                            _checkAbortStatus = true;
                                            _logger.Information("ABORT-Updating " + operationGroups.ProgressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId + " in UpdateWorkflowOperationgroupInfra");

                                            resultStatus = true;
                                        }
                                    }
                                    WorklowActionResultList = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.ReferenceId);
                                    var errList = WorklowActionResultList.Where(x => x.Status == "Error").ToList();

                                    if (errList.Count >= 1)
                                    {
                                        resultStatus = false;

                                    }

                                    while (!resultStatus)
                                    {
                                        await Task.Delay(2000);
                                        //CheckThreadState();
                                        _logger.Debug("Waiting for result status getting True");
                                        // await _cachedCustomersAsyncLock.WaitAsync();
                                        _logger.Debug("Entering into GetWorkflowOperationGroupsId");


                                        operationGroups = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(CurrentWorkflowOperationGroup.ReferenceId);
                                        _logger.Debug("Getting CurrentWorkflowOperationGroupId from GetWorkflowOperationGroupsId: " + CurrentWorkflowOperationGroup.ReferenceId);

                                        WorklowActionResultList = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.ReferenceId);

                                        _logger.Debug("Getting CurrentWorkflowOperationGroupId from GetWorkflowActionResultByWorkflowOperationGroupId: " + CurrentWorkflowOperationGroup.ReferenceId + " " + WorklowActionResultList);
                                        _logger.Debug("Entering into WaitForCondition,  waiting for condition execution");
                                        resultStatus = await _workflowHelper.WaitForConditionNew(i, resultStatus, actionResultStatus, operationGroups, WorklowActionResultList,
                                            totalActionCount, dynJsonWorkflow, nodeList, _interpreterManager, _serviceProvider);
                                        _logger.Debug("Getting all details from WaitForCondition: " + resultStatus);

                                        var errList2 = WorklowActionResultList.Where(x => x.Status == "Error").ToList();

                                        if (errList2.Count == 0)
                                        {
                                            resultStatus = true;
                                            // break;
                                        }
                                        //resultStatus = result2.Result;
  
                                        WorklowActionResultList = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.ReferenceId);

                                        var errList1 = WorklowActionResultList.Where(x => x.Status == "Error").ToList();

                                        if (errList1.Count == 0)
                                        {
                                            resultStatus = true;
                                            // break;
                                        }
                                        if (errList1.Count >= 1)
                                        {
                                            var totalSuccessCount = WorklowActionResultList
                                           .Where(x => x.Status.Equals("success", StringComparison.OrdinalIgnoreCase) ||
                                            x.Status.Equals("skip", StringComparison.OrdinalIgnoreCase) ||
                                            x.Status.Equals("Bypassed", StringComparison.OrdinalIgnoreCase))
                                           .ToList();

                                            _logger.Information($"RunParallelAction - Total successful/skipped actions: {totalSuccessCount.Count}");

                                            var progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";

                                            actionResultStatus = "Error";
                                            await _workflowHelper.UpdateWorkflowOperationGroupInfra1(actionResultStatus, progressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId);
                                            resultStatus = false;

                                            bool shouldAbort = WorklowActionResultList.Any(x =>
                                            (x.Status == "Aborted" && x.ConditionActionId == (int)ConditionalOperation.ABORT) ||
                                            (x.Status == "Error" && x.ConditionActionId == (int)ConditionalOperation.ABORT) ||
                                            (x.Status == "Aborted"));
                                            _logger.Information($"shouldAbort conditions :{shouldAbort}");
                                            if (shouldAbort)
                                            {
                                                _checkAbortStatus = true;

                                                actionResultStatus = "Aborted";
                                                await _workflowHelper.UpdateWorkflowOperationGroupInfra2(
                                                    actionResultStatus,
                                                    progressStatus,
                                                    operationGroups.CurrentActionName,
                                                    operationGroups.CurrentActionId,
                                                    (int)ConditionalOperation.ABORT);
                                                resultStatus = true;
                                            }
                                        }

                                    }
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine(e);

                                }
                                nodeList.Clear();
                            }
                        }
                        else
                        {
                            nodeList.Add(stepId, getNodeValue);
                        }
                    }
                  
                    else
                    {
                        nodeList.Clear();
                        var parallelAction = new ParallelAction(CurrentWorkflowOperationGroup, _logger, _dataProvider,
                            _serviceProvider);
                        parallelAction.TotalActionCount = 0;
                        ThreadPool.SetMinThreads(1000, 1000);
                        actionResultStatus =
                            await parallelAction.RunParallelAction(_interpreterManager, totalActionCount, getNodeValue, stepId,false);
                        var actionInfo = dynJsonWorkflow.SelectToken($"nodes[{i}].actionInfo");
                        bool isConditional = actionInfo.Value<bool>("IsConditional");
                        if (isConditional)
                        {
                            var conditionDetails = actionInfo.Value<JArray>("conditionDetails");
                            List<JObject> conditionDetailsList = conditionDetails?.ToObject<List<JObject>>() ?? new List<JObject>();
                            var conditionStatus1 = conditionDetails?[0].Value<string>("condition");
                            var conditionStatus2 = conditionDetailsList.Count > 1 ? conditionDetails?[1].Value<string>("condition") : null;
                            var groupIndex = ResolveGroupIndex(dynJsonWorkflow, conditionDetails, conditionDetailsList?.Count);

                            int successIndex = GetConditionIndex(conditionStatus1, conditionStatus2, "ifcondition",
                                conditionDetails);
                            int failIndex = GetConditionIndex(conditionStatus1, conditionStatus2, "elsecondition",
                                conditionDetails);
                            int failureCount = GetConditionFailureCount(conditionStatus1, conditionStatus2, "elsecondition",
                                conditionDetails);

                            var skipFailedIndexCount = 0;
                            var failOccurCount = 0;
                            int nextIndex = i + 1;
                            int uptoSkipIndexSuccess = successIndex != 0 ? successIndex - 1 : 0;
                            int uptoSkipIndexFail = failIndex != 0 ? failIndex - 1 : 0;
                            if (successIndex != 0 && nextIndex < uptoSkipIndexSuccess && actionResultStatus == "Success")
                            {
                               await UpdateSkipAction(totalActionCount, nextIndex, uptoSkipIndexSuccess, dynJsonWorkflow,parallelAction,CurrentWorkflowOperationGroup.ReferenceId);
                            }
                            if (failIndex != 0 && nextIndex < uptoSkipIndexFail && actionResultStatus == "Error")
                            {
                               await UpdateSkipAction(totalActionCount, nextIndex, uptoSkipIndexFail, dynJsonWorkflow,parallelAction,CurrentWorkflowOperationGroup.ReferenceId);
                            }
                            if (actionResultStatus == "Error")
                            {
                                var wfActionId = actionInfo.SelectToken("actionType")?.Value ?? "UnknownActionType";
                                await parallelAction.SkipForWorkflowAction(totalActionCount, stepId, actionName, wfActionId, CurrentWorkflowOperationGroup.ReferenceId,false);
                            }

                            if (groupIndex != 0)
                            {
                                if (actionResultStatus == "Success")
                                {
                                    i = groupIndex - 1;
                                }
                                else if (failOccurCount < failureCount)
                                {
                                    i = groupIndex - 1;
                                    failOccurCount++;
                                    continue;
                                }
                            }
                            else
                            {
                                if (actionResultStatus == "Success" && successIndex != 0)
                                {
                                    i = successIndex - 2;
                                    skipFailedIndexCount = failIndex != 0 ? failIndex - 1 : 0;
                                }
                                else if (actionResultStatus != "Success" && failIndex != 0)
                                {
                                    i = failIndex - 2;
                                    continue;
                                }
                            }

                        }


                        bool resultStatus = false;

                        if (actionResultStatus == "Error")
                        {
                            _logger.Information("Sequential Action Result is failed to wait for ConditionAction:" +
                                                actionResultStatus);
                            resultStatus = false;
                        }
                        else
                        {
                            resultStatus = true;
                        }

                        var operationGroups =
                            await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(
                                CurrentWorkflowOperationGroup.ReferenceId);
                        var WorklowActionResultList =
                            await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(
                                CurrentWorkflowOperationGroup.ReferenceId);


                        if (operationGroups.ConditionalOperation == 4)
                        {
                            var WorklowActionResult =
                                await _dataProvider.WorkflowActionResult
                                    .GetWorkflowActionResultByWorkflowOperationGroupId(operationGroups.ReferenceId);
                            var actionResultId = WorklowActionResult.FirstOrDefault(x => x.ConditionActionId == 4);

                            if (actionResultId != null)
                            {
                                _logger.Information(
                                    "ABORT-ConditionalOperation 4 : Abort Button clicked for ActionResultId:" +
                                    actionResultId.ReferenceId);
                                await _workflowHelper.WorkflowActionResultStatusUpdate("Aborted",
                                    actionResultId.ReferenceId);
                                _logger.Information("ABORT-Updating status Abort and actionResultId: " +
                                                    actionResultId.ReferenceId);
                                await _workflowHelper.UpdateWorkflowOperationGroupInfra("Aborted",
                                    operationGroups.ProgressStatus, operationGroups.CurrentActionName,
                                    operationGroups.CurrentActionId);
                                _checkAbortStatus = true;
                                _logger.Information("ABORT-Updating " + operationGroups.ProgressStatus,
                                    operationGroups.CurrentActionName,
                                    operationGroups.CurrentActionId + " in UpdateWorkflowOperationgroupInfra");

                                resultStatus = true;
                            }
                        }

                        while (!resultStatus)
                        {
                            getNodeValue = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".actionInfo");

                            await _workflowHelper.CheckThreadState(_cancellationTokenSource);

                            operationGroups =
                                await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(
                                    CurrentWorkflowOperationGroup.ReferenceId);
                            WorklowActionResultList =
                                await _dataProvider.WorkflowActionResult
                                    .GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup
                                        .ReferenceId);

                            await Task.Delay(2000);

                            var result2 = await _workflowHelper.WaitForCondition(i, resultStatus, actionResultStatus,
                                operationGroups, WorklowActionResultList, totalActionCount);
                            resultStatus = result2;
                            //Reload
                            if (operationGroups.ConditionalOperation == 3)
                            {
                                _logger.Information("ConditionalOperation 3 : Reload Button clicked");
                                var getrebindWorkflow =
                                    await _dataProvider.Workflow.GetWorkflowById(operationGroups.WorkflowId);
                                _logger.Information("Getting workflowId from GetWorkflowById " +
                                                    operationGroups.WorkflowId);
                                if (getrebindWorkflow != null)
                                {
                                    dynJsonWorkflow =
                                        Newtonsoft.Json.JsonConvert.DeserializeObject(getrebindWorkflow.Properties);

                                }
                                else
                                {
                                    // Handle the case when the workflow is not found
                                    // Set resultStatus to false or take appropriate action
                                }
                            }

                            if (operationGroups.ConditionalOperation == 2)
                            {

                                var actionResultList = WorklowActionResultList
                                    .Where(x => (x.ConditionActionId == 2) && (x.Status == "Error")).ToList();

                                foreach (var actionResultlist in actionResultList)
                                {
                                    if (actionResultList != null)
                                    {
                                        operationGroups =
                                            await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(
                                                operationGroups.ReferenceId);

                                        _logger.Information(
                                            "ParallelExecutor-ConditionalOperation 2 : Retry Button clicked for ActionResultId:" +
                                            actionResultlist.ReferenceId);
                                        await _workflowHelper.WorkflowActionResultStatusUpdate("Retry",
                                            actionResultlist.ReferenceId);
                                        _logger.Information(
                                            "ParallelExecutor-ConditionalOperation 2 : Retry Button clicked for ActionResultName:" +
                                            actionResultlist.WorkflowActionName);
                                        _logger.Information(
                                            "ParallelExecutor-ConditionalOperation 2 : Retry Button clicked for ActionStepId:" +
                                            actionResultlist.StepId);


                                        _logger.Information(
                                            "ParallelExecutor-Update Status RETRY and ActionResultId in WorkflowActionResultStatusUpdate");
                                        await _dataProvider.WorkflowActionResult.DeleteWorkflowActionResultById(
                                            actionResultlist.ReferenceId);
                                        _logger.Information(
                                            "RETRY-Deleting actionResultId from DeleteWorkflowActionResultById: " +
                                            actionResultlist.ReferenceId);
                                        await _workflowHelper.UpdateWorkflowOperationGroupInfra("Running",
                                            operationGroups.ProgressStatus, operationGroups.CurrentActionName,
                                            operationGroups.CurrentActionId);
                                        _logger.Debug("Update Running " + operationGroups.ProgressStatus,
                                            operationGroups.CurrentActionName,
                                            operationGroups.CurrentActionId + " in UpdateWorkflowOperationgroupInfra");

                                        actionResultStatus =
                                            await parallelAction.RunParallelAction(_interpreterManager, totalActionCount, getNodeValue,
                                                stepId,false);
                                        _logger.Information("ConditionalOperation 3 : Retry Status:" +
                                                            actionResultStatus);

                                        resultStatus = actionResultStatus == "Success";

                                    }
                                }
                            }
                        }
                    }
                }

                return true;
            }
            catch (Exception exc)
            {

                _logger.Error("Exception occurring in RunParallelWorkflow " + exc.Message);
            }
            finally
            {
                // Unsubscribe from the event before terminating modules to avoid triggering the event after termination
                    _interpreterManager.OnInterpreterCreated -= InterpreterCreated;

                    // Terminate the interpreter modules after script processing
                    _interpreterManager.TerminateModules();

                    _logger.Information("RunActionScript: Interpreter modules terminated.");
                 
            }
            return false;
        }
        private int? GetConditionIndex(string conditionStatus1, string conditionStatus2, string targetCondition, JArray conditionDetails)
        {

            int? nextAction = 0;
            if (targetCondition == conditionStatus1)
            {
                nextAction = conditionDetails[0].Value<int?>("index");
                _logger.Information($"Next Action Index : {nextAction}");
                return nextAction;
            }
            else if (targetCondition == conditionStatus2)
            {
                nextAction = conditionDetails[1].Value<int?>("index");
                _logger.Information($"Next Action Index : {nextAction}");
                return nextAction;
            }
            return 0;

            //return (conditionStatus1 == targetCondition ? conditionDetails[0].Value<int?>("index") : 0)
            //       ?? (conditionStatus2 == targetCondition ? conditionDetails[1].Value<int>("index") : 0);
        }
        private int? GetConditionFailureCount(string conditionStatus1, string conditionStatus2, string targetCondition, JArray conditionDetails)
        {
            if (conditionStatus1 == targetCondition)
            {
                return conditionDetails[0].Value<int?>("failureCount");
            }
            else if (conditionStatus2 == targetCondition)
            {
                return conditionDetails[1].Value<int>("failureCount");
            }
            return 0;
            //return (conditionStatus1 == targetCondition ? conditionDetails[0].Value<int?>("failureCount") : 0)
            //?? (conditionStatus2 == targetCondition ? conditionDetails[1].Value<int>("failureCount") : 0);
        }

        private int ResolveGroupIndex(JToken? dynJsonWorkflow, JArray conditionDetails, int? conditionList)
        {
            string groupCondition = conditionDetails[0].Value<string>("gotoAction")!;

            if (!groupCondition.Contains("group"))
                groupCondition = conditionList > 1 ? conditionDetails[1].Value<string>("gotoAction") : null;
            if (!string.IsNullOrEmpty(groupCondition))
            {
                for (int i = 0; i < dynJsonWorkflow?.SelectToken("nodes")?.Count(); i++)
                {
                    if (dynJsonWorkflow.SelectToken($"nodes[{i}].groupId")?.ToString() == groupCondition)
                        return i;
                }
            }

            return 0;
        }
        public async Task<string> UpdateSkipAction(int totalActionCount, int index, int Targetindex, dynamic dynJsonWorkflow,dynamic parallelAction,string WorkflowOperationGroupId)
        {

            for (int i = index; i < Targetindex; i++)
            {
                var _actionInfo = dynJsonWorkflow.SelectToken($"nodes[{i}].actionInfo");
                var actionName = _actionInfo.SelectToken("actionName").Value;
                var stepId = dynJsonWorkflow.SelectToken($"nodes[{i}].stepId")!.Value;
                var wfActionId = _actionInfo.SelectToken("actionType")?.Value ?? "UnknownActionType";
                await parallelAction.SkipForWorkflowAction(totalActionCount, stepId, actionName, wfActionId, WorkflowOperationGroupId);
            }
            return "";
        }

        private void InterpreterCreated(object? sender, EventArgs e)
        {
            if (sender is Interpreter interpreter)
            {
                interpreter.OnOutput += Print;
            }
        }
        private void Print(object? sender, OutputAvailableEventArgs e)
        {
            _logger.Information(e.Output);
        }

        #region IDisposable Members

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                }
            }

            _isDisposed = true;
        }

        ~ParallelWorkflowManager()
        {
            Dispose(false);
        }

        #endregion
    }
}
