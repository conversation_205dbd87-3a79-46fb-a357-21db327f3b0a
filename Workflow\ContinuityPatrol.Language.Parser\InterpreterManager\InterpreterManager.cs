﻿using Serilog;
using System.Collections.Concurrent;

namespace ContinuityPatrol.Language.Parser.InterpreterManager
{
    public class InterpreterManager
    {
        private readonly ILogger _logger;

        public event EventHandler? OnInterpreterCreated;

        public Interpreter? CurrentInterpreter { get; private set; }

        public InterpreterManager(ILogger logger)
        {
            _logger = logger;
        }
        private ConcurrentDictionary<int, Interpreter> Interpreters { get; } = new();

        static int _nextId = 1;
        public int LastId => _nextId - 1;
        public List<IModule> Modules { get; set; } = new();

        public List<Interpreter> AllInterpreters => Interpreters.Values.ToList();

        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1000, 10000);
        public async Task<Interpreter> NewInterpreterAsync()
        {
            try
            {
                Interpreter interpreter;

                await _semaphore.WaitAsync(); // Wait asynchronously for access to the critical section
                try
                {
                    interpreter = new Interpreter(_nextId++, _logger);

                    _logger.Debug("Interpreter HashCode : " + interpreter.GetHashCode());

                    // Attempt to add interpreter to the dictionary
                    bool added = Interpreters.TryAdd(interpreter.Id, interpreter);
                    if (!added)
                        throw new InvalidOperationException("Failed to add interpreter.");

                    _logger.Debug("Interpreter List Count :" + AllInterpreters.Count);
                }
                finally
                {
                    _semaphore.Release(); // Always release the semaphore once we're done
                }
                foreach (var module in Modules.Where(m => m != null))
                {
                    await module.CreateInstance(interpreter); // Assuming CreateInstance can be awaited or offloaded
                }

                OnInterpreterCreated?.Invoke(interpreter, EventArgs.Empty);

                return interpreter;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to create new interpreter.");
                return null!; // Handle exception and return 0 to indicate failure.
            }
        }
        public int NewInterpreter()
        {
            try
            {
                Interpreter interpreter;
                lock (Interpreters)
                {
                    interpreter = new Interpreter(_nextId++, _logger);
                    if (!Interpreters.TryAdd(interpreter.Id, interpreter))
                        throw new InvalidOperationException("Failed to add interpreter.");
                }

                foreach (var module in Modules.Where(m => m != null))
                {
                    module.CreateInstance(interpreter);
                }

                OnInterpreterCreated?.Invoke(interpreter, EventArgs.Empty);

                return interpreter.Id;

            }
            catch (Exception e)
            {
                _logger.Error(e, "Failed to create new interpreter.");

                return 0; // Handle exception and return 0 to indicate failure.
            }

        }

        public bool RemoveInterpreter(int interpreterHandle)
        {
            if (!Interpreters.TryGetValue(interpreterHandle, out var interpreter))
                return false;

            if (interpreter == CurrentInterpreter)
                return false; // Prevent removing the current interpreter.

            return Interpreters.TryRemove(interpreterHandle, out _);
        }

        public bool SetInterpreter(int interpreterHandle)
        {

            if (!Interpreters.TryGetValue(interpreterHandle, out var interpreter))
                return false;

            CurrentInterpreter = interpreter;
            Interpreter.LastInstance = interpreter;
            return true;
        }

        public async Task<bool> SetInterpreterAsync(Interpreter interpreter)
        {
            if (interpreter == null)
            {
                return false;
            }
            return await Task.Run(() =>
            {
                CurrentInterpreter = interpreter;
                Interpreter.LastInstance = interpreter;
                return true;
            });
        }

        public void TerminateModules()
        {
            foreach (var module in Modules.Where(module => module != null))
            {
                try
                {
                    module.Terminate();
                }
                catch (Exception)
                {
                    // Log or handle exceptions here if necessary.
                }
            }
        }

        public int GetInterpreterHandle(Interpreter interpreter)
        {
            return Interpreters.FirstOrDefault(kv => kv.Value == interpreter).Key;
        }
        public Interpreter GetInterpreter(int handle)
        {
            return Interpreters.TryGetValue(handle, out var interpreter) ? interpreter : null!;
        }

        public void AddModule(IModule module, Interpreter interpreter)
        {
            Modules.Add(module);

            module.CreateInstance(interpreter);
        }
    }
}
