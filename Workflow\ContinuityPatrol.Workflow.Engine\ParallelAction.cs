﻿using System.Text.RegularExpressions;
using ContinuityPatrol.CrossCuttingConcerns.Shared;
using ContinuityPatrol.Data.Db;
using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.Language.Parser;
using ContinuityPatrol.Language.Parser.InterpreterManager;
using Serilog.Context;


namespace ContinuityPatrol.Workflow.Engine
{
    public class ParallelAction
    {
        public WorkflowOperationGroup CurrentWorkflowOperationGroup { get; set; }
        private bool _isDisposed;
        private WorkflowActionReturnResult _workflowActionReturnResult;
        private readonly ILogger _logger;
        private IDataProvider _dataProvider;
        public WorkflowOperationGroup WorkflowOperationGroup { get; set; }
        public int TotalActionCount { get; set; }

        private IServiceProvider _apiserviceProvider;

        private readonly ActionHelper _actionHelper;

        public Interpreter InterpreterInstance { get; private set; }

        private InterpreterManagerModule _interpreterManager;

        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(500, 1000);

        private bool _checkAbortStatus = false;
        public ParallelAction(WorkflowOperationGroup groupWorkflow, ILogger logger, IDataProvider dataProvider, IServiceProvider apiserviceProvider)
        {
            CurrentWorkflowOperationGroup = groupWorkflow;
            _logger = logger;
            _dataProvider = dataProvider;
            _apiserviceProvider = apiserviceProvider;
            _actionHelper = new ActionHelper(groupWorkflow, dataProvider, logger);
        }

        public async Task<string> RunParallelAction(InterpreterManagerModule interpreterManager, int totalActionCount, dynamic getNodeValue, string stepId, bool IsParallel)
        {
            try
            {
                _interpreterManager = interpreterManager;

                await CreateInterpreterInstance();

                var errorMessage = "";

                _logger.Debug("Entered into RunParallelAction");

                var properties = getNodeValue.SelectToken("properties");

                var actionName = "";

                _logger.Debug($"RunParallelAction- Workflow Properties {properties}");

                if (getNodeValue.SelectToken("actionName") != null)
                {
                    actionName = getNodeValue.SelectToken("actionName").Value;

                    LogContext.PushProperty("ActionName", actionName);

                    await UpdateWorkflowOperationGroup(actionName);
                }

                _logger.Information("RunParallelAction - ActionName " + actionName);

                var wfActionId = getNodeValue.SelectToken("actionType").Value;

                _logger.Information("RunParallelAction- wfActionId " + wfActionId);

                var isCustom = CurrentWorkflowOperationGroup.IsCustom;

                if (isCustom)
                {
                    var isCheckedCustom = getNodeValue.SelectToken("isCustom");
                    if (isCheckedCustom != null)
                    {
                        var isCheckedAction = getNodeValue.SelectToken("isCustom").Value;

                        if (!isCheckedAction)
                        {
                            return await _actionHelper.SkipUncheckedCustomAction(totalActionCount, stepId, actionName, wfActionId, IsParallel);
                        }
                    }
                }
                var workflowAction = await _dataProvider.WorkflowAction.GetWorkflowActionById(wfActionId);

              

                if (workflowAction.ActionName == "WaitForParallelAction")
                {
                    await _actionHelper.WaitForParallelActionCompleted();
                }
                if (workflowAction.ActionName == "VerifyParallelActionStatus")
                {
                    
                    JObject jsonObject = JObject.Parse($"{properties}");
                    JArray dependentWorkflowActions = (JArray)jsonObject["@@DependentWorkflowAction"]!;
                    List<string> parallelActionStepId = dependentWorkflowActions.ToObject<List<string>>()!;
                    bool status= await _actionHelper.WaitForVerifyParallelAction(parallelActionStepId);
                    return status==true? "Success":"Error";
                }
                _logger.Information("RunParallelAction-Getting wfActionId from GetWorkflowActionById " + wfActionId);
                //string actionType = string.Empty;
                //string eventType = string.Empty;
                string actionId = wfActionId;
                //string finalDescription = String.Empty;
                // dynamic dynJsonWorkflowAction = Newtonsoft.Json.JsonConvert.DeserializeObject(getWorkflowAction.Properties);


                if (workflowAction.ActionName == "DisplayAlert")
                {
                    await _actionHelper.ExecuteDisplayAlertAsync(actionName, wfActionId, stepId, totalActionCount);
                    return "Success";
                }
                var workflowActionProperties = getNodeValue.SelectToken("properties");

                if (workflowAction.ActionName.ToLower() == "waitforworkflowaction")
                {
                    //await CheckNullOrEmpty_BeforeWorkflow_Initialize(workflowActionProperties, "workflowActionProperties is null while waitforworkflowaction in ", "RunAction", actionName, wfActionId, stepId);
                    await DependentAction(workflowActionProperties);

                    return await SkipWaitForWorkflowAction(totalActionCount, stepId, actionName, wfActionId);
                }

                WorkflowActionReturnResult workflowActionReturnResult = await _actionHelper.InsertWorkflowActionResult(actionName, wfActionId, stepId, "Running", IsParallel);

                

                var finalScript = workflowAction.Script;

              

                foreach (var actionProperty in workflowActionProperties)
                {
                    string newValue = actionProperty.Value.ToString();
                    string oldValue = actionProperty.Value.ToString();
                    string propertyName = actionProperty.Name.ToString();

                    if (finalScript == null)
                        continue;

                    finalScript = finalScript.Replace(propertyName, newValue);
                    // Process each line in the script
                    foreach (var scriptLine in finalScript.Split('\n'))
                    {
                        if (scriptLine.Contains("dll.execute"))
                        {
                            finalScript = ProcessDllExecute(scriptLine, oldValue, finalScript);
                        }
                        //else if (scriptLine.Contains("powershell.commands"))
                        //{
                        //    //finalScript = ProcessPowerShellCommands(scriptLine, oldValue, finalScript);
                        //}
                    }
                }
                //commentwithoutguiautomation
                //if (finalScript != null)
                //{
                //    if (!finalScript.Contains("dll.execute"))
                //        finalScript = finalScript.Replace("\"@@", "").Replace("@@\"", "");
                //}

                if (finalScript!.Contains("guiautomation"))
                {
                    finalScript = finalScript.Replace("guiactionname", actionName);
                    finalScript = Regex.Replace((string)finalScript, @"""@@(\w+)@@""", match =>
                    {
                        string variable = match.Groups[1].Value;
                        return variable;
                    });
                }
                else
                {
                    if (!finalScript.Contains("dll.execute"))
                    {
                        finalScript = finalScript.Replace("\"@@", "").Replace("@@\"", "");
                    }
                }


                var script = finalScript;

                var getWorkflowOperationList = await _dataProvider.WorkflowOperation.GetWorkflowOperationById(CurrentWorkflowOperationGroup.WorkflowOperationId);
                string mode = CurrentWorkflowOperationGroup.ActionMode.ToLower();

                string actionResultStatus = "";

                string updateworkflowoperationstatusResult = string.Empty;
                if (workflowAction.ActionName.ToLower() == "updateworkflowoperationstatus")
                {
                    var splitResult = script.Split("=")[1].Split("@$@");
                    var infrId = splitResult[0].TrimEnd();
                    var operationStatus = splitResult[1].TrimEnd(';');
                    updateworkflowoperationstatusResult = await _actionHelper.UpdateDrOperationStatus(infrId, operationStatus.Trim());

                }
                if (workflowAction.ActionName.ToLower() == "executecpl")
                {
                    script = await RunActionScript(script, actionResultStatus, mode);
                }
                _logger.Debug("\r\n" + "**************************Script starts:*****************************" + "\r\n" + script + "\r\n" + "**************************ScriptEnd*****************************");
                try
                {
                    _logger.Information($"RunParallelAction - Action Name: {workflowAction.ActionName}");

                    switch (workflowAction.ActionName)
                    {
                        case "UpdateWorkflowOperationStatus":
                            _logger.Information("RunParallelAction - UpdateWorkflowOperationStatus");
                            actionResultStatus = updateworkflowoperationstatusResult;
                            break;

                        case "WaitForParallelAction":
                            actionResultStatus = "Success";
                            break;

                        default:
                            _logger.Information("RunParallelAction - Script starts");
                            actionResultStatus = await RunActionScript(script, actionResultStatus, mode);
                            break;
                    }

                    actionResultStatus = ProcessResultStatus(actionResultStatus);

                    actionResultStatus = await SplitResultWithOutput(actionResultStatus);
                }
                catch (Exception exc)
                {
                    errorMessage = exc.Message;

                    _logger.Error("Exception occurring in RunParallelAction: " + errorMessage);

                    //if (exc.Message.Contains("$@$"))
                    //{
                    //    var matchedOutput = exc.Message.Split(new[] { "$@$" }, StringSplitOptions.None)[1];

                    //    actionResultStatus = exc.Message.Split(new[] { "$@$" }, StringSplitOptions.None)[0];

                    //    _logger.Error("Output Not Matched With:" + matchedOutput);
                    //}

                    actionResultStatus = "Error";
                }
                _logger.Information("RunParallelAction-RunActionScript result is: " + actionResultStatus);

                if (string.IsNullOrEmpty(actionResultStatus))
                {
                    actionResultStatus = "Error";
                }
                if (actionResultStatus != "Success")
                {
                    actionResultStatus = "Error";
                }
                _logger.Information($"RunParallelAction - ActionResultStatus: {actionResultStatus}");

                _logger.Information($"RunParallelAction - IsParallel: {IsParallel}");

                await _actionHelper.UpdateWorkflowActionResultStatus(workflowActionReturnResult.workflowActionResultId, actionResultStatus, actionName, actionId, errorMessage, stepId, IsParallel);

                // Retrieve workflow action results for the current operation group
                var workflowActionResultList = await _dataProvider.WorkflowActionResult
                    .GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.ReferenceId);

                _logger.Information($"RunParallelAction - Retrieved WorkflowActionResult for Group ID: {CurrentWorkflowOperationGroup.ReferenceId}");

                // Calculate success and skip count
                var totalSuccessCount = workflowActionResultList
                    .Where(x => x.Status.Equals("success", StringComparison.OrdinalIgnoreCase) ||
                                x.Status.Equals("skip", StringComparison.OrdinalIgnoreCase) ||
                                x.Status.Equals("Bypassed", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                _logger.Information($"RunParallelAction - Total successful/skipped actions: {totalSuccessCount.Count}");

                var progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";

                if (totalSuccessCount.Count == totalActionCount)
                {
                    _logger.Information($"RunParallelAction - TotalSuccessCount {totalSuccessCount.Count} equals TotalActionCount {totalActionCount}");

                    if (!string.Equals(actionResultStatus, "Error", StringComparison.OrdinalIgnoreCase))
                    {
                        await _actionHelper.UpdateWorkflowOperationGroupInfra("Completed", progressStatus, actionName, wfActionId, string.Empty);
                        _logger.Information("RunParallelAction - Updated actionResultStatus to Completed");
                    }
                    await _actionHelper.UpdateWorkflowActionResultStatus(workflowActionReturnResult.workflowActionResultId, actionResultStatus, actionName, actionId, errorMessage, stepId,IsParallel);

                }
                else
                {
                    await _actionHelper.UpdateWorkflowOperationGroupInfra(actionResultStatus, progressStatus, actionName, wfActionId, string.Empty);
                    await _actionHelper.UpdateWorkflowActionResultStatus(workflowActionReturnResult.workflowActionResultId, actionResultStatus, actionName, actionId, errorMessage, stepId, IsParallel);

                }
                // Update status and progress in WorkflowOperationInfra (GroupWorkflowTable)

                _logger.Information($"RunParallelAction - Updated ActionResultStatus: {actionResultStatus}, ProgressStatus: {progressStatus}, ActionName: {actionName}, WfActionId: {wfActionId} in UpdateWorkflowOperationGroupInfra"
                );

                return actionResultStatus;
            }
            catch (Exception exc)
            {

                _logger.Error("Exception Occurring in RunParallelAction " + exc.Message);
                throw;
            }
        }

 
        public async Task<string> SplitResultWithOutput(string actionResultStatus)
        {
            if (actionResultStatus.Contains("$@$"))
            {
                var matchedOutput = actionResultStatus.Split(new[] { "$@$" }, StringSplitOptions.None)[1];
                actionResultStatus = actionResultStatus.Split(new[] { "$@$" }, StringSplitOptions.None)[0];
             
                _logger.Information($"Executed Action Result with : {actionResultStatus}");
                _logger.Information($"Output Matched With: {matchedOutput}");
            }

            return actionResultStatus;
        }

        private string ProcessDllExecute(string scriptLine, string oldValue, string finalScript)
        {
            if (string.IsNullOrEmpty(oldValue))
                return finalScript;

            string transformedValue = oldValue.Replace("+", "$Plus$").Replace("-", "$hypen$").Replace(">", "$greater$").Replace("=", "$equals$").Replace(":", "$cln$").Replace(".", "$dot$").Replace(" ", "$Space$").Replace("_", "$Uscore$").Replace("*", "$Star$").Replace(",", "$Comma$")
                .Replace(">", "$__$");

            string updatedLine = scriptLine.Replace(oldValue, transformedValue);

            if (finalScript.Contains("//"))
            {
                finalScript = finalScript.Replace("://", "$#$");
            }

            finalScript = finalScript
                .Replace("/", "___")
                .Replace(scriptLine, updatedLine);

            return finalScript;
        }

        private string ProcessPowerShellCommands(string scriptLine, string oldValue, string finalScript)
        {
            string transformedValue = oldValue.Replace("-", "$_$");
            string updatedLine = scriptLine.Replace(oldValue, transformedValue);

            return finalScript.Replace(oldValue, transformedValue);
        }
        private string ProcessResultStatus(string status)
        {
            if (status.Contains("$@$"))
            {
                var parts = status.Split(new[] { "$@$" }, StringSplitOptions.None);
                status = parts[0];
                _logger.Information($"Output Matched With: {parts[1]}");
                return status;
            }
            return status;
        }

        private async Task UpdateWorkflowOperationGroup(string actionName)
        {
            var workflowOperationGroup = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(CurrentWorkflowOperationGroup.ReferenceId);

            workflowOperationGroup.Status = "Running";

            workflowOperationGroup.NodeId = workflowOperationGroup.NodeId;

            workflowOperationGroup.CurrentActionName = actionName;

            await _dataProvider.WorkflowOperationGroup.UpdateWorkflowOperationGroup(workflowOperationGroup);
        }
        public async Task<string> RunActionScript(string script, string actionResultStatus, string mode)
        {
            _logger.Information("RunActionScript method starts.");

            try
            {


                // Process the script asynchronously
                string result = await ProcessScript(script);

                _logger.Information($"RunActionScript: Script processing result: {result}");

                // Return the result of the script processing
                return result;
            }
            catch (Exception exc)
            {
                _logger.Error(
                    $"RunActionScript: Exception occurred while creating InterpreterManagerModule: {exc.Message}");
                _logger.Error($"Stack Trace: {exc.StackTrace}");
                throw; // Rethrow the exception to propagate it
            }
        }

        private async Task CreateInterpreterInstance()
        {
            await _semaphore.WaitAsync();

            try
            {
                const int maxRetries = 3;
                int retryCount = 0;
                bool interpreterSetSuccessfully = false;

                _interpreterManager.OnInterpreterCreated += InterpreterCreated;
                _interpreterManager.Modules = new InterpreterMangerFactory().GetModuleList(_logger, _apiserviceProvider, ServiceType.WORKFLOW_SERVICE, "");

                while (retryCount < maxRetries && !interpreterSetSuccessfully)
                {
                    try
                    {
                        var interpreterId = await _interpreterManager.NewInterpreterAsync();
                        _logger.Debug($"RunActionScript: Interpreter Id Created: {interpreterId}");

                        interpreterSetSuccessfully = await _interpreterManager.SetInterpreterAsync(interpreterId);

                        if (!interpreterSetSuccessfully)
                        {
                            _logger.Warning($"RunActionScript: Failed to set interpreter on attempt {retryCount + 1}. Retrying...");
                        }
                        else
                        {
                            _logger.Debug("RunActionScript: Interpreter set successfully.");
                            break;
                        }
                    }
                    catch (Exception exc)
                    {
                        _logger.Error($"RunActionScript: Exception during interpreter creation or setting: {exc.Message}");
                    }

                    retryCount++;

                    if (retryCount < maxRetries)
                    {
                        await Task.Delay(500);
                    }
                }

                if (!interpreterSetSuccessfully)
                {
                    _logger.Error("RunActionScript: Failed to set interpreter after multiple attempts.");
                    throw new Exception("Failed to set interpreter after multiple retries.");
                }

                const int maxInstanceRetries = 3;
                var instanceRetryCount = 0;

                while (instanceRetryCount < maxInstanceRetries && InterpreterInstance == null)
                {
                    InterpreterInstance = _interpreterManager.CurrentInterpreter;

                    if (InterpreterInstance == null)
                    {
                        _logger.Error($"RunActionScript: InterpreterInstance is null on attempt {instanceRetryCount + 1}.");

                        instanceRetryCount++;

                        await Task.Delay(500);
                    }
                    else
                    {
                        _logger.Debug("RunActionScript: InterpreterInstance set successfully.");
                        break;
                    }
                }

                if (InterpreterInstance == null)
                {
                    _logger.Error("RunActionScript: Failed to set InterpreterInstance after multiple retries.");
                    throw new Exception("InterpreterInstance is null after multiple retries.");
                }

                _interpreterManager.CreateInstance(InterpreterInstance);

                _logger.Debug("RunActionScript: InterpreterInstance created successfully.");

                _semaphore.Release();
            }
            catch (Exception exc)
            {
                _logger.Error($"RunActionScript: Exception occurred while creating InterpreterManagerModule: {exc.Message}");

                throw;
            }
            finally
            {
                _semaphore.Release();
            }
        }
        private void InterpreterCreated(object? sender, EventArgs e)
        {

        }
        private async Task<string> ProcessScript(string script)
        {
            try
            {
                var result = await InterpreterInstance.ProcessAsync(script);

                //var result = await Task.Run(()=> InterpreterInstance.Process(_logger,"",script));

                return result.ToString();
            }
            catch (Exception)
            {
                InterpreterInstance.InvalidateStacksAfterLevel(0);
                throw;
            }
        }

        public async Task<string> SkipForWorkflowAction(dynamic totalActionCount, string stepId, string actionName, string actionId,string WorkflowOperationGroupId,bool isParallel)
        {
            await _actionHelper.InsertWorkflowActionResult(actionName, actionId, stepId, "skip", isParallel);

            //CheckPropertyIsNullOrEmpty(WorkflowOperationGroup.ReferenceId, "WorkflowOperationGroup.ReferenceId is null in ", "SkipForWorkflowAction");
            var workflowActionResults = await _dataProvider.WorkflowActionResult
                .GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroupId);

            _logger.Information($"{actionName}: Customized execution - Skipped unchecked action");

            var successfulActions = workflowActionResults
                .Where(x => new[] { "success", "skip", "bypassed" }.Contains(x.Status.ToLower().Trim()))
                .ToList();

            _logger.Information($"Customized execution - Completed action count: {successfulActions.Count}");

            var progressStatus = $"{successfulActions.Count}/{totalActionCount}";
            var status = "skip";

            await _actionHelper.UpdateWorkflowOperationGroupInfra(status, progressStatus, actionName, actionId, "");

            _logger.Debug($"{actionName}: Updated Action Status: {status} | Progress Status: {progressStatus}");

            return status;
        }

        private async Task<string> SkipWaitForWorkflowAction(dynamic totalActionCount, string stepId, string actionName, string actionId)
        {
            await _actionHelper.InsertWorkflowActionResult(actionName, actionId, stepId, "Success", false);

            CheckPropertyIsNullOrEmpty(CurrentWorkflowOperationGroup.WorkflowOperationId, "WorkflowOperationGroup.ReferenceId is null in ", "SkipWaitForWorkflowAction");

            var workflowActionResults = await _dataProvider.WorkflowActionResult
                .GetWorkflowActionResultByWorkflowOperationGroupId(CurrentWorkflowOperationGroup.WorkflowOperationId);

            _logger.Information($"{actionName}: Customized execution - Skipped unchecked action");

            var successfulActions = workflowActionResults
                .Where(x => new[] { "success", "skip", "bypassed" }.Contains(x.Status.ToLower().Trim()))
                .ToList();

            _logger.Information($"Customized execution - Completed action count: {successfulActions.Count}");

            var progressStatus = $"{successfulActions.Count}/{totalActionCount}";
            var status = "Success";

            await _actionHelper.UpdateWorkflowOperationGroupInfra(status, progressStatus, actionName, actionId, "");

            _logger.Debug($"{actionName}: Updated Action Status: {status} | Progress Status: {progressStatus}");

            return status;
        }

        private async Task DependentAction(dynamic workflowActionProperties)
        {
            var workflowId = string.Empty;

            var stepId = string.Empty;

            foreach (var actionProperty in workflowActionProperties)
            {
                string name = actionProperty.Name.ToString().ToLower();
                string value = actionProperty.Value.ToString();

                if (name.Contains("workflow_name") || name.Contains("dependentworkflowname"))
                {
                    workflowId = value;
                }

                if (name.Contains("workflow_action") || name.Contains("dependentworkflowaction"))
                {
                    stepId = value;
                }
            }
            if (!string.IsNullOrEmpty(workflowId) && !string.IsNullOrEmpty(stepId))
            {
                await _actionHelper.WaitForWorkflowActionCompleted(workflowId, stepId);
            }
            else
            {
                _logger.Warning("Missing workflow or action information. WorkflowId or StepId is empty.");
            }
        }


        void CheckPropertyIsNullOrEmpty(dynamic propertyValue, string errorMessage, string methodName)
        {
            if (string.IsNullOrEmpty(propertyValue?.ToString()))
            {
                string message = errorMessage + "" + "MethodName :" + methodName + "," + "ClassName :" + "SequentialActionExecutor.";
                _logger.Error(message);
                throw new Exception(message);
            }
        }


    }
}