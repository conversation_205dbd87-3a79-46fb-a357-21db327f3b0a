﻿using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Contract.Persistence;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.Data.Shared.Responses;
using ContinuityPatrol.LoadBalancer.Contracts;
using ContinuityPatrol.LoadBalancer.Exceptions;
using ContinuityPatrol.LoadBalancer.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;

public class WorkflowInitiativeServiceTests
{
    private readonly Mock<ILogger> _mockLogger;
    private readonly Mock<IDataProvider> _mockDataProvider;
    private readonly Mock<IWindowService> _mockWindowService;
    private readonly Mock<IServiceScopeFactory> _mockServiceScopeFactory;
    private readonly Mock<IServiceScope> _mockScope;
    private readonly Mock<IServiceProvider> _mockServiceProvider;
    private readonly Mock<ILeastConnectionLoadAlgorithm> _mockLoadAlgorithm;
    private readonly Mock<ILogger> _loggerMock = new Mock<ILogger>();

    private readonly WorkflowInitiativeService _service;

    public WorkflowInitiativeServiceTests()
    {
        _mockLogger = new Mock<ILogger>();
        _mockDataProvider = new Mock<IDataProvider>();
        _mockWindowService = new Mock<IWindowService>();
        _mockServiceScopeFactory = new Mock<IServiceScopeFactory>();
        _mockScope = new Mock<IServiceScope>();
        _mockServiceProvider = new Mock<IServiceProvider>();
        _mockLoadAlgorithm = new Mock<ILeastConnectionLoadAlgorithm>();
        var _loggerMock = new Mock<Serilog.ILogger>();
        // Setup service provider mock to return mocks
        _mockServiceProvider.Setup(x => x.GetService(typeof(IWindowService))).Returns(_mockWindowService.Object);
        _mockServiceProvider.Setup(x => x.GetService(typeof(IDataProvider))).Returns(_mockDataProvider.Object);
        _mockScope.Setup(x => x.ServiceProvider).Returns(_mockServiceProvider.Object);
        _mockServiceScopeFactory.Setup(x => x.CreateScope()).Returns(_mockScope.Object);

        _service = new WorkflowInitiativeService(_loggerMock.Object, _mockServiceScopeFactory.Object, _mockLoadAlgorithm.Object);
    }

    [Theory]
    [InlineData("op-001", "ref-001", "profile-1", "wf-1", true)]
    [InlineData("op-002", "ref-002", "profile-2", "wf-2", true)]
    public async Task InitiateLoadBalancerWorkflowOperation_ReturnsSuccess_ForMultipleInputs(
     string operationId, string referenceId, string profileId, string workflowId, bool expectedSuccess)
    {
        // Arrange
        var mockWorkflowOperationRepo = new Mock<IWorkflowOperationService>();

        var inputGroups = new List<WorkflowOperationGroup>
    {
        new WorkflowOperationGroup { ReferenceId = "g1", Status = "Pending", WorkflowId = "hi", NodeId = "NodeConf_RefId1" },
        new WorkflowOperationGroup { ReferenceId = "g2", Status = "Pending", WorkflowId = "hello", NodeId = "NodeConf_RefId2" },
        new WorkflowOperationGroup { ReferenceId = "g3", Status = "Pending", WorkflowId = "world", NodeId = "NodeConf_RefId2" },
        new WorkflowOperationGroup { ReferenceId = "g4", Status = "Pending", WorkflowId = "tamil", NodeId = "" },
        new WorkflowOperationGroup { ReferenceId = "g5", Status = "Pending", WorkflowId = "hell", NodeId = "" },
        new WorkflowOperationGroup { ReferenceId = "g6", Status = "Pending", WorkflowId = "grass", NodeId = "" },
        new WorkflowOperationGroup { ReferenceId = "g7", Status = "Pending", WorkflowId = "well", NodeId = "" }
    };

        var workflowOperation = new WorkflowOperation { Id = 1, ReferenceId = referenceId, ProfileId = profileId };
        var nodes = new List<NodeConfiguration>
    {
        new NodeConfiguration { ReferenceId = "NodeConf_RefId1", Id = 1, Type = "WorkflowService", TypeCategory = "CPNode" },
        new NodeConfiguration { ReferenceId = "NodeConf_RefId2", Id = 2, Type = "WorkflowService", TypeCategory = "CPNode" }
    };
        var profile = new WorkflowProfile { ExecutionPolicy = "0" };

        _mockDataProvider.Setup(x => x.WorkflowOperation).Returns(mockWorkflowOperationRepo.Object);

        _mockDataProvider.Setup(x => x.WorkflowOperation.GetWorkflowOperationById(operationId))
            .ReturnsAsync(workflowOperation);

        // Mock for the current operation's group (main input)
        _mockDataProvider.Setup(x => x.WorkflowOperationGroup.GetWorkflowOperationGroupsByOperationId(referenceId))
            .ReturnsAsync(inputGroups);

        // Mock for filtering logic (g1, g2, g3)
        _mockDataProvider.Setup(x => x.WorkflowOperationGroup.GetWorkflowOperationGroupsByOperationId("g1"))
            .ReturnsAsync(new List<WorkflowOperationGroup> {
            new WorkflowOperationGroup { ReferenceId = "g1", NodeId = "NodeConf_RefId", Status = "Pending" }
            });

        _mockDataProvider.Setup(x => x.WorkflowOperationGroup.GetWorkflowOperationGroupsByOperationId("g2"))
            .ReturnsAsync(new List<WorkflowOperationGroup> {
            new WorkflowOperationGroup { ReferenceId = "g2", NodeId = "NodeConf_RefId", Status = "Pending" }
            });

        _mockDataProvider.Setup(x => x.WorkflowOperationGroup.GetWorkflowOperationGroupsByOperationId("g3"))
            .ReturnsAsync(new List<WorkflowOperationGroup>()); // or null intentionally if required

        _mockDataProvider.Setup(x => x.Workflow.GetWorkflowById(It.IsAny<string>()))
            .ReturnsAsync(new Workflow { Id = 999 });

        _mockDataProvider.Setup(x => x.WorkflowProfile.GetWorkflowProfileByReferenceId(profileId))
            .ReturnsAsync(profile);

        _mockDataProvider.Setup(x => x.NodeConfiguration.GetNodeConfigurationList())
            .ReturnsAsync(nodes);

        _mockWindowService.Setup(x => x.GetNodeServiceAsync(It.IsAny<string>()))
            .ReturnsAsync(new BaseResponse { Success = true });

        _mockDataProvider.Setup(x => x.WorkflowOperation.GetAllWorkflowOperations())
            .ReturnsAsync(new List<WorkflowOperation>
            {
            new WorkflowOperation { ReferenceId = "g1", Status = "Running" },
            new WorkflowOperation { ReferenceId = "g2", Status = "Pending" },
            new WorkflowOperation { ReferenceId = "g3", Status = "Completed" }
            });

        // Act
        var result = await _service.InitiateLoadBalancerWorkflowOperation(operationId,false);

        // Assert
        Assert.Equal(expectedSuccess, result.Success);
    }

    [Fact]
    public async Task InitiateLoadBalancerWorkflowOperation_ThrowsException_WhenWorkflowOperationIsNull()
    {
        // Arrange
        _mockDataProvider.Setup(x => x.WorkflowOperation.GetWorkflowOperationById("invalid-id"))
            .ReturnsAsync((WorkflowOperation)null);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidException>(() =>
            _service.InitiateLoadBalancerWorkflowOperation("invalid-id",false));
    }
    //[Theory]
    // [InlineData("op-001", "ref-001", "profile-1", "wf-1", true)]
    // [InlineData("op-002", "ref-002", "profile-2", "wf-2", true)]
    // public async Task InitiateLoadBalancerWorkflowOperation_ReturnsSuccess_ForMultipleInputs(
    // string operationId, string referenceId, string profileId, string workflowId, bool expectedSuccess)
    // {
    //     // Arrange
    //     var mockWorkflowOperationRepo = new Mock<IWorkflowOperationService>();
    //     var groups = new List<WorkflowOperationGroup> {
    //             new WorkflowOperationGroup { ReferenceId = "g1", Status = "Pending",WorkflowId="hi" },
    //             new WorkflowOperationGroup { ReferenceId = "g2", Status = "Pending",WorkflowId="hello" }
    //         };

    //     var workflowOperation = new WorkflowOperation { Id = 1, ReferenceId = referenceId, ProfileId = profileId };

    //     var workflow = new Workflow { Id = 1 };
    //     var nodes = new List<NodeConfiguration>
    // {
    //     new NodeConfiguration {ReferenceId="NodeConf_RefId", Id = 1, Type = "WorkflowService", TypeCategory = "CPNode" },
    // };
    //     var profile = new WorkflowProfile { ExecutionPolicy = "0" };
    //     // Mock the data provider and window service
    //     _mockDataProvider.Setup(x => x.WorkflowOperation).Returns(mockWorkflowOperationRepo.Object);

    //     _mockDataProvider.Setup(x => x.WorkflowOperation.GetWorkflowOperationById(operationId))
    //         .ReturnsAsync(workflowOperation);
    //     _mockDataProvider.Setup(x => x.WorkflowOperationGroup.GetWorkflowOperationGroupsByOperationId(referenceId))
    //         .ReturnsAsync(groups);
    //     //_mockDataProvider.Setup(x => x.Workflow.GetWorkflowById(workflowId))
    //     //    .ReturnsAsync(workflow);
    //     _mockDataProvider.Setup(x => x.Workflow.GetWorkflowById(It.IsAny<string>()))
    // .ReturnsAsync(new Workflow { Id = 999 });
    //     _mockDataProvider.Setup(x => x.WorkflowProfile.GetWorkflowProfileByReferenceId(profileId))
    //         .ReturnsAsync(profile);
    //     _mockDataProvider.Setup(x => x.NodeConfiguration.GetNodeConfigurationList())
    //         .ReturnsAsync(nodes);
    //     _mockWindowService.Setup(x => x.GetNodeServiceAsync(It.IsAny<string>()))
    //         .ReturnsAsync(new BaseResponse { Success = true });
    //     _mockDataProvider.Setup(x => x.WorkflowOperation.GetAllWorkflowOperations())
    // .ReturnsAsync(new List<WorkflowOperation>
    // {
    //     new WorkflowOperation {ReferenceId = "g1", Status = "Running" },
    //     new WorkflowOperation {ReferenceId = "g2", Status = "Pending" },
    //     new WorkflowOperation {ReferenceId = "g3", Status = "Completed" } // This one will be filtered out
    // });

    //     // Act
    //     var result = await _service.InitiateLoadBalancerWorkflowOperation(operationId);

    //     // Assert
    //     Assert.Equal(expectedSuccess, result.Success);
    // }
}

