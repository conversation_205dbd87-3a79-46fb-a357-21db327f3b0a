﻿using ContinuityPatrol.CrossCuttingConcerns.Helper;
using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.Workflow.Engine.UpdateMonitorSolutions.Contract;

namespace ContinuityPatrol.Workflow.Engine;

public class MonitorServiceFactory
{
    private readonly IDataProvider _dataProvider;
    private readonly ILogger _logger;
    private readonly Dictionary<string, IMonitorUpdater> _updateMethods;

    public MonitorServiceFactory(IDataProvider dataProvider, ILogger logger)
    {
        _dataProvider = dataProvider;
        _logger = logger;
        _updateMethods = [];
    }

    public async Task UpdateMonitorServiceLogStatus(Job job, string json, string type, string dataLag, string infraId, string errorMessage)
    {
        try
        {
            _logger.Information("Entered into UpdateMonitorServiceLogStatus");

            string namespaceName = "ContinuityPatrol.Workflow.Engine.UpdateMonitorSolutions.Models";
            Assembly assembly = Assembly.GetExecutingAssembly();

            var classesInNamespace = assembly.GetTypes().Where(t => string.Equals(t.Namespace, namespaceName, StringComparison.OrdinalIgnoreCase) && t.IsClass && t.Name.ToLower() == type.ToLower()).FirstOrDefault();
            if (classesInNamespace != null)
            {
                var updaterInstance = Activator.CreateInstance(classesInNamespace!, _dataProvider, _logger) as IMonitorUpdater;
                _updateMethods.Add(type.ToLower(), updaterInstance!);
            }

            var dataLagStatus = string.IsNullOrWhiteSpace(json) || json.Equals("Error") ? "UnHealthy" : "Healthy";
            var infraObject = await _dataProvider.InfraObject.GetInfraObjectById(infraId);
            var businessFunction = await _dataProvider.BusinessFunction.GetBusinessFunctionById(infraObject.BusinessFunctionId);

            if (TryGetUpdateMethod(type, out var updateMethod))
            {
                var monitorStatus = await updateMethod!.Update(job, businessFunction, json, type, dataLag, infraObject);
                await UpdateDashBoardView(monitorStatus, infraObject.ReferenceId, json, type, dataLag);
                await UpdateJobAndNodeWorkflowExecution(json, job, monitorStatus, errorMessage);
            }
            else
            {
                _logger.Warning($"Unsupported monitor type: {type}");
            }

            _logger.Information("UpdateMonitorServiceLogStatus ended.");
        }
        catch (Exception ex)
        {
            _logger.Error($"Error updating monitor service log status: {ex.Message}");
        }
    }

    public bool TryGetUpdateMethod(string type, out IMonitorUpdater? updateMethod)
    {
        return _updateMethods.TryGetValue(type.ToLower(), out updateMethod);
    }

    public async Task UpdateDashBoardView(MonitorServiceStatus monitorStatus, string infraId, string json, string type, string dataLag)
    {
        try
        {
            _logger.Debug($"Entered into UpdateDashBoardView method");

            var statuses = new List<string>();
            var errorMessage = string.Empty;

            var infraObject = await _dataProvider.InfraObject.GetInfraObjectById(infraId);
            if (infraObject is null)
            {
                _logger.Error("InfraObject is null while perform update monitoring db operations.");
                return;
            }

            if (!string.IsNullOrEmpty(infraObject.ServerProperties))
            {
                var serverJson = JObject.Parse(infraObject.ServerProperties);
                var serverProperties = PropertyValueModifier.GetPropertyNames(serverJson);
                foreach (var property in serverProperties)
                {
                    var serverId = serverJson.SelectToken($"{property}.id")?.ToString();
                    serverId = !string.IsNullOrEmpty(serverId) && serverId.Contains(',') ? serverId.Split(',')[0] : serverId;
                    if (string.IsNullOrEmpty(serverId))
                    {
                        continue;
                    }
                    var server = await _dataProvider.Server.GetServerById(serverId);
                    if (server != null && string.IsNullOrWhiteSpace(server.Status))
                    {
                        continue;
                    }
                    statuses.Add(server!.Status);
                    if (server.Status.Equals("down", StringComparison.OrdinalIgnoreCase))
                    {
                        var ipAddress = JsonConvert.DeserializeObject<dynamic>(server!.Properties)?.SelectToken("IpAddress");
                        errorMessage = $"The {property} server '{server?.Name}' with IP address '{ipAddress}' under the InfraObject '{infraObject.Name}' is currently unreachable.";
                    }
                }
            }

            if (!string.IsNullOrEmpty(infraObject.DatabaseProperties))
            {
                var dbJson = JObject.Parse(infraObject.DatabaseProperties);
                var dbProperties = PropertyValueModifier.GetPropertyNames(dbJson);
                foreach (var property in dbProperties)
                {
                    var databaseId = dbJson.SelectToken($"{property}.id")?.ToString();
                    databaseId = !string.IsNullOrEmpty(databaseId) && databaseId.Contains(',') ? databaseId.Split(',')[0] : databaseId;
                    if (string.IsNullOrEmpty(databaseId))
                    {
                        continue;
                    }
                    var database = await _dataProvider.Database.GetDatabaseById(databaseId);
                    if (database != null && string.IsNullOrWhiteSpace(database.ModeType))
                    {
                        continue;
                    }
                    statuses.Add(database!.ModeType);
                    if (database.ModeType.Equals("down", StringComparison.OrdinalIgnoreCase))
                    {
                        var server = await _dataProvider.Server.GetServerById(database.ServerId);
                        var ipAddress = JsonConvert.DeserializeObject<dynamic>(server!.Properties)?.SelectToken("IpAddress");
                        errorMessage = $"The primary database '{database.Name}' with IP address '{ipAddress}' within the infraObject '{infraObject}' is currently down.";
                    }
                }
            }

            var result = statuses.All(x => x.Equals("up", StringComparison.OrdinalIgnoreCase));
            var status = result ? "Available" : "Not available";

            var dataLagValueSplitHour = dataLag.Trim();
            TimeSpan timeSpanValue;

            try
            {
                if (dataLagValueSplitHour.StartsWith("+")) dataLagValueSplitHour = dataLagValueSplitHour[1..];
                timeSpanValue = TimeSpan.Parse(dataLagValueSplitHour);
            }
            catch (FormatException)
            {
                _logger.Error($"Invalid TimeSpan format: {dataLagValueSplitHour}");
                timeSpanValue = TimeSpan.Zero;
            }

            var minutes = (int)Math.Round(timeSpanValue.TotalMinutes);
            var businessFunction = await _dataProvider.BusinessFunction.GetBusinessFunctionById(infraObject.BusinessFunctionId);
            int configuredRpo = Convert.ToInt32(businessFunction.ConfiguredRPO);
            if (Convert.ToInt32(minutes) > configuredRpo && status.Equals("available", StringComparison.OrdinalIgnoreCase)) status = "Major Impact";

            var businessService = await _dataProvider.BusinessService.GetBusinessServiceById(infraObject.BusinessServiceId);
            var monitorValue = monitorStatus?.ReferenceId ?? (await _dataProvider.MonitorServiceStatus.GetMonitorStatusByInfraId(infraObject.ReferenceId, type)).ReferenceId;

            string standardizedInput = await StandardizeDateSeparator(monitorStatus!.LastModifiedDate.ToString());
            if (DateTime.TryParseExact(standardizedInput, "dd-MM-yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime parsedDateTime)) standardizedInput = parsedDateTime.ToString("dd-MM-yyyy HH:mm:ss");

            var dashboardView = await _dataProvider.DashboardView.GetDashboardViewByInfraId(infraObject.ReferenceId);
            if (dashboardView != null)
            {
                await _dataProvider.DashboardView.UpdateDashboardViewById(new DashboardView
                {
                    Id = dashboardView.Id,
                    ReferenceId = dashboardView.ReferenceId,
                    InfraObjectId = infraObject.ReferenceId,
                    InfraObjectName = infraObject.Name,
                    BusinessServiceId = infraObject.BusinessServiceId,
                    BusinessServiceName = infraObject.BusinessServiceName,
                    BusinessFunctionId = infraObject.BusinessFunctionId,
                    BusinessFunctionName = infraObject.BusinessFunctionName,
                    CompanyId = dashboardView.CompanyId,
                    Properties = json,
                    MonitorType = type,
                    DROperationStatus = infraObject.DROperationStatus,
                    DataLagValue = dataLag.ToLower().Contains("session") ? "NA" : dataLag,
                    Status = status,
                    EntityId = monitorValue,
                    Type = infraObject.Type,
                    State = infraObject.State,
                    CurrentRPO = dataLag.ToLower().Contains("session") ? "NA" : dataLag,
                    RPOThreshold = businessFunction.RPOThreshold,
                    CurrentRTO = dashboardView.CurrentRTO,
                    ConfiguredRPO = dashboardView.ConfiguredRPO,
                    ConfiguredRTO = dashboardView.ConfiguredRTO,
                    ErrorMessage = errorMessage,
                    ReplicationStatus = 3,
                    SiteProperties = businessService.SiteProperties,
                    Priority = businessService.Priority,
                    IsDRReady = dashboardView.IsDRReady,
                    CreatedBy = dashboardView.CreatedBy,
                    LastModifiedBy = dashboardView.LastModifiedBy,
                    RPOGeneratedDate = monitorStatus?.LastModifiedDate.ToString(),
                    RTOGeneratedDate = dashboardView.RTOGeneratedDate,
                    EstimatedRTO = dashboardView.EstimatedRTO
                });
                _logger.Debug("Dashboard View Updated");
            }

            await _dataProvider.DashboardViewLogs.CreateDashboardViewLog(
                new DashboardViewLog
                {
                    InfraObjectId = infraObject.ReferenceId,
                    InfraObjectName = infraObject.Name,
                    BusinessServiceId = infraObject.BusinessServiceId,
                    BusinessServiceName = infraObject.BusinessServiceName,
                    BusinessFunctionId = infraObject.BusinessFunctionId,
                    BusinessFunctionName = infraObject.BusinessFunctionName,
                    Properties = json,
                    CompanyId = infraObject.CompanyId,
                    DROperationStatus = infraObject.DROperationStatus,
                    MonitorType = type,
                    DataLagValue = dataLag.ToLower().Contains("session") ? "NA" : dataLag,
                    Status = status,
                    EntityId = monitorValue,
                    Type = infraObject.Type,
                    State = infraObject.State,
                    CurrentRPO = dataLag.ToLower().Contains("session") ? "NA" : dataLag,
                    RPOThreshold = businessFunction.RPOThreshold,
                    CurrentRTO = dashboardView?.CurrentRTO,
                    ConfiguredRPO = businessFunction.ConfiguredRPO,
                    ConfiguredRTO = businessFunction.ConfiguredRTO,
                    ErrorMessage = errorMessage,
                    SiteProperties = businessService.SiteProperties,
                    Priority = businessService.Priority
                });

            _logger.Debug("Dashboard View Log inserted");
            _logger.Debug($"UpdateDashBoardView method ended");
        }
        catch (Exception ex)
        {
            _logger.Error("UpdateDashBoardView method throws exception." + ex.Message);
        }
    }

    private async Task UpdateJobAndNodeWorkflowExecution(string actionResultStatus, Job monitorJob, MonitorServiceStatus serviceStatus, string errorMessage)
    {
        try
        {
            _logger.Debug("Entered into UpdateJobAndNodeWorkflowExecution");

            var job = await _dataProvider.Job.GetJobById(monitorJob.ReferenceId);
            if (job != null)
            {
                job.Status = string.IsNullOrWhiteSpace(actionResultStatus) || actionResultStatus.ToLower().Equals("error") ? "Error" : "Success";
                job.IsSchedule = 2;
                job.LastExecutionTime = serviceStatus.LastModifiedDate.ToString();
                job.ExceptionMessage = string.IsNullOrWhiteSpace(actionResultStatus) || actionResultStatus.ToLower().Equals("error") ? errorMessage : string.Empty;
                await _dataProvider.Job.UpdateJobById(job);
            }

            var nodeWorkflowExecution = await _dataProvider.NodeWorkflowExecution.GetNodeWorkflowExecutionByJobId(job.ReferenceId);
            if (nodeWorkflowExecution != null)
            {
                nodeWorkflowExecution.Status = string.IsNullOrWhiteSpace(actionResultStatus) || actionResultStatus.ToLower().Equals("error") ? "Error" : "Success";
                await _dataProvider.NodeWorkflowExecution.UpdateNodeWorkflowExecutionDetailById(nodeWorkflowExecution);
            }

            _logger.Debug("UpdateJobAndNodeWorkflowExecution method ended");
        }
        catch (Exception exc)
        {
            _logger.Error("Exception occurring in UpdateJobAndNodeWorkflowExecution " + exc.Message);
        }
    }

    public static Task<string> StandardizeDateSeparator(string input)
    {
        return Task.FromResult(Regex.Replace(input, @"[\/\.]", "-"));
    }
}