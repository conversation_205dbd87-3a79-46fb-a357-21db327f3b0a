﻿using ContinuityPatrol.CrossCuttingConcerns.Extensions;
using ContinuityPatrol.CrossCuttingConcerns.Helper;
using ContinuityPatrol.CrossCuttingConcerns.Shared;
using ContinuityPatrol.Data.Db.Repositories;
using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.ExceptionHandler;
using ContinuityPatrol.Language.Parser;
using ContinuityPatrol.Language.Parser.InterpreterManager;
using ContinuityPatrol.Plugins.Common.Variables;
using ContinuityPatrol.Plugins.Common.Variables;

namespace ContinuityPatrol.Workflow.Engine;

public class SequentialActionExecutor : IActionExecutor
{
    static readonly ILogger Log = Serilog.Log.ForContext<SequentialActionExecutor>();
    private readonly string _infraId;
    private WorkflowAction _workflowAction;
    private WorkflowActionReturnResult _workflowActionReturnResult;
    private DRReadiness _drReadiness;
    private MonitorService _monitorService;
    private ReplicationService _replicationService;
    public Job Job { get; set; }
    public ReplicationJobModel ReplicationJob { get; set; }
    public InfraObject InfraObject { get; set; }
    public InfraobjectScheduler InfraobjectScheduler { get; set; }
    public WorkflowOperationGroup WorkflowOperationGroup { get; set; }
    public CancellationTokenSource CancellationTokenSource { get; set; }
    public InfraobjectScheduler InfraObjectScheduler { get; set; }
    public MonitorServiceModel MonitorServiceStatus { get; set; }
    public CyberAirGap CyberAirGap { get; set; }
    public Interpreter InterpreterInstance { get; set; }
    public IDataProvider DataProvider { get; set; }
    public IServiceProvider ServiceProvider { get; set; }
    public ResiliencyReadyWorkflowScheduleLogs ResiliencyReadyWorkflowScheduleLogs { get; set; }
    public ILogger Logger { get; set; }
    public ServiceType CurrentServiceType { get; set; }
    public int TotalActionCount { get; set; }
    private InterpreterManagerModule _interpreterManager;
    private readonly ActionHelper _actionHelper;
    private MonitorServiceConfiguration _monitorServiceConfiguration;

    public SequentialActionExecutor(ILogger logger, IDataProvider dataProvider, IServiceProvider apiServiceProvider, MonitorServiceModel monitorServicestatus, string infra)
    {
        Logger = logger;
        DataProvider = dataProvider;
        MonitorServiceStatus = monitorServicestatus;
        ServiceProvider = apiServiceProvider;
        _infraId = infra;
    }
    public SequentialActionExecutor(Job job, ILogger logger, IDataProvider dataProvider, IServiceProvider apiServiceProvider, string infraId)
    {
        Job = job;
        Logger = logger;
        DataProvider = dataProvider;
        ServiceProvider = apiServiceProvider;
        _infraId = infraId;
    }
    public SequentialActionExecutor(ReplicationJobModel job, ILogger logger, IDataProvider dataProvider, IServiceProvider apiServiceProvider, string infraId)
    {
        ReplicationJob = job;
        Logger = logger;
        DataProvider = dataProvider;
        ServiceProvider = apiServiceProvider;
        _infraId = infraId;
    }
    public SequentialActionExecutor(WorkflowOperationGroup groupWorkflow, ILogger logger, IDataProvider dataProvider, IServiceProvider apiServiceProvider, CancellationTokenSource cancellationToken)
    {
        WorkflowOperationGroup = groupWorkflow;
        Logger = logger;
        DataProvider = dataProvider;
        ServiceProvider = apiServiceProvider;
        CancellationTokenSource = cancellationToken;
        _actionHelper = new ActionHelper(groupWorkflow, dataProvider, logger);
    }
    public SequentialActionExecutor(ILogger logger, IDataProvider dataProvider, InfraobjectScheduler infraScheduler, InfraObject infraObject, IServiceProvider apiServiceProvider)
    {
        Logger = logger;
        DataProvider = dataProvider;
        InfraObject = infraObject;
        InfraobjectScheduler = infraScheduler;
        ServiceProvider = apiServiceProvider;
    }
    public SequentialActionExecutor(ILogger logger, IDataProvider dataProvider, InfraobjectScheduler infraScheduler, InfraObject infraObject, IServiceProvider apiServiceProvider, ResiliencyReadyWorkflowScheduleLogs resiliencyReadyWorkflowScheduleLogs)
    {
        Logger = logger;
        DataProvider = dataProvider;
        InfraObject = infraObject;
        InfraobjectScheduler = infraScheduler;
        ServiceProvider = apiServiceProvider;
        ResiliencyReadyWorkflowScheduleLogs = resiliencyReadyWorkflowScheduleLogs;
        _actionHelper = new ActionHelper(resiliencyReadyWorkflowScheduleLogs, dataProvider, logger);
    }
    public SequentialActionExecutor(ILogger logger, IDataProvider dataProvider, IServiceProvider serviceProvider)
    {
        Logger = logger;
        DataProvider = dataProvider;
        ServiceProvider = serviceProvider;
    }
    public SequentialActionExecutor(ILogger logger, IDataProvider dataProvider, IServiceProvider serviceProvider, CyberAirGap cyberAirGap)
    {
        Logger = logger;
        DataProvider = dataProvider;
        ServiceProvider = serviceProvider;
        CyberAirGap = cyberAirGap;
    }

    public async Task<string> RunAction(Interpreter interpreter, int totalActionIndex, int index, int totalActionCount, dynamic actionInfo, ServiceType type, string stepId, string actionName)
    {
        var errorMessage = string.Empty;
        var command = string.Empty;
        var actionResultStatus = string.Empty;
        var mode = string.Empty;
        InterpreterInstance = interpreter;

        CurrentServiceType = type;

        try
        {
            Logger.Debug("Entered into RunAction method");

            actionName = actionInfo.SelectToken("actionName")?.Value ?? string.Empty;

            Logger.Debug($"Executing Action Name: {actionName}");

            string wfActionId = actionInfo.SelectToken("actionType").Value;

            _workflowAction = await DataProvider.WorkflowAction.GetWorkflowActionById(wfActionId);
            var workflowActionProperties = actionInfo.SelectToken("properties");
            if (_workflowAction.ActionName.ToLower() == "waitforworkflowaction")
            {
                //await CheckNullOrEmpty_BeforeWorkflow_Initialize(workflowActionProperties, "workflowActionProperties is null while waitforworkflowaction in ", "RunAction", actionName, wfActionId, stepId);
                await DependentAction(workflowActionProperties);

                return await SkipWaitForWorkflowAction(totalActionCount, stepId, actionName, wfActionId);
            }

            Logger.Debug($"{actionName} : Workflow ActionId : {wfActionId}");



            await CheckNullOrEmpty_BeforeWorkflow_Initialize(_workflowAction, "_workflowAction is null while retrieve _workflowAction from actionInfo.SelectToken() in ", "RunAction", actionName, wfActionId, stepId);

            if (_workflowAction == null)
            {
                Logger.Error("Workflow action retrieval failed.");

                return "Failure"; // Or handle as appropriate
            }


            var formActionName = type == ServiceType.WORKFLOW_SERVICE ? _workflowAction.ActionName : string.Empty;

            if (_workflowAction.ActionName == "WaitForParallel")
            {
                await _actionHelper.WaitForParallelActionCompleted();
            }


            await CheckNullOrEmpty_BeforeWorkflow_Initialize(workflowActionProperties, "workflowActionProperties is null while retrieve workflowActionProperties from actionInfo.SelectToken() in ", "RunAction", actionName, wfActionId, stepId);

            if (type == ServiceType.WORKFLOW_SERVICE)
            {
                if (WorkflowOperationGroup.ActionMode.ToLower() == "simulate")
                {
                    if (_workflowAction.Type.ToLower().Contains("operation"))
                    {
                        await DrOperationType(workflowActionProperties);

                        return await SkipDrCustomAction(totalActionCount, stepId, formActionName, wfActionId);
                    }
                }
            }

            Log.ForContext("WorkflowActionId", wfActionId);

            if (type == ServiceType.WORKFLOW_SERVICE && WorkflowOperationGroup.IsCustom)
            {
                var isCustom = WorkflowOperationGroup.IsCustom;

                if (isCustom)
                {
                    var isCheckedCustom = actionInfo.SelectToken("isCustom");
                    if (isCheckedCustom != null)
                    {
                        var isCheckedAction = actionInfo.SelectToken("isCustom").Value;

                        if (!isCheckedAction)
                        {
                            return await _actionHelper.SkipUncheckedCustomAction(totalActionCount, stepId, formActionName, wfActionId, false);
                        }
                    }
                }
            }
            Thread.Sleep(500);
            if (_workflowAction.ActionName == "DisplayAlert")
            {
                Thread.Sleep(1000);
                await _actionHelper.ExecuteDisplayAlertAsync(actionName, wfActionId, stepId, totalActionCount);
                return "Success";
            }
            var finalScript = _workflowAction.Script;
            var actionIndex = totalActionIndex + 1;
            var actionNameWithSerial = $"{actionIndex}. {_workflowAction.ActionName}";

            if (type is ServiceType.WORKFLOW_SERVICE or ServiceType.RESILIENCE_READY_SERVICE or ServiceType.CYBER_AIRGAP or ServiceType.CYBER_RESILIENCY or ServiceType.MONITOR_SERVICE_STATUS)
            {
                try
                {
                    string message = await BuildMessageForAction(workflowActionProperties);

                    finalScript = UpdateFinalScript(finalScript, workflowActionProperties, type);

                    Logger.LogActionInfo(actionNameWithSerial, message);
                }
                catch (Exception ex)
                {
                    _workflowActionReturnResult = await _actionHelper.InsertWorkflowActionResult(actionName, wfActionId, stepId, "Error", false);
                    throw new Exception(ex?.Message);
                }
            }
            if (type == ServiceType.CYBER_RESILIENCY && _workflowAction.ActionName == "UpdateWorkflowScheduleStatus")
            {
                Logger.Debug("Entered into RunAction CYBER_RESILIENCY and UpdateWorkflowScheduleStatus");
                var status = finalScript.Split("=")[1].Trim(';');

                return await _actionHelper.UpdateWorkflowScheduleStatuslog(ResiliencyReadyWorkflowScheduleLogs.ReferenceId, status);
            }

            if (type == ServiceType.CYBER_RESILIENCY && _workflowAction.ActionName == "CGStatusReport")
            {
                var Path = finalScript.Split("=")[1].Trim(';');
                Logger.Debug("Entered into RunAction CGStatusReport");
                var res = await _actionHelper.LoadCGReport_Scheduler(ResiliencyReadyWorkflowScheduleLogs.ReferenceId, Path);
                return actionResultStatus = res == "Success" ? "Success" : "Error";
            }
            else if (_workflowAction.ActionName == "CGStatusReport")
            {
                var Path = finalScript.Split("=")[1].Trim(';');
                Logger.Debug("Entered into RunAction CGStatusReport");
                _workflowActionReturnResult = await _actionHelper.InsertWorkflowActionResult(actionName, wfActionId, stepId, "Running", false);
                var res = await _actionHelper.LoadCGReport(WorkflowOperationGroup.WorkflowOperationId, Path);

                if (res == "Success")
                {
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Success", actionName, wfActionId, "", stepId, false);
                }
                else
                {
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Error", actionName, wfActionId, "", stepId, false);
                }

                var workflowActionResults = await DataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);
                var successStatuses = new HashSet<string> { "success", "skip", "bypassed", "skipped" };
                //var totalSuccessCount = workflowActionResults
                //    .Where(x => successStatuses.Contains(x.Status.ToLower().Trim()))
                //    .ToList();
                var totalSuccessCount = workflowActionResults.Count(x =>
                new[] { "success", "skip", "bypassed", "skipped" }.Contains(x.Status.ToLower().Trim())
            );
                string progressStatus = $"{totalSuccessCount}/{totalActionCount}";
                if (totalSuccessCount == totalActionCount)
                {
                    await _actionHelper.UpdateWorkflowOperationGroupInfra("Completed", progressStatus, actionName, wfActionId, errorMessage);
                    actionResultStatus = ResultStatus.COMPLETED.ToTitleCase();

                }
                else
                {
                    await _actionHelper.UpdateWorkflowOperationGroupInfra(res, progressStatus, actionName, wfActionId, errorMessage);
                    actionResultStatus = res;
                }
                return actionResultStatus;
            }

            if (type == ServiceType.CYBER_RESILIENCY && _workflowAction.ActionName == "RP4VM_MultipleCG_TestCopy")
            {
                var messageParts = finalScript.Split(new[] { "$@$" }, StringSplitOptions.None);

                string Serverid = messageParts[0].Replace("Result=", "");
                string Cluster = messageParts[1];
                string Wait = messageParts[2];
                string TestingNetwork = messageParts[3];
                string PoweroncopyVMsduringtesting = messageParts[4];
                int BatchCount = messageParts[5].ParseToInt();
                string ProductionVcenterName = messageParts[6];
                string ReplicaVcenterName = messageParts[7];
                string ProductionClusterName = messageParts[8];
                dynamic server = await DataProvider.Server.GetServerById(Serverid);
                RPforVMMultipleCGTestCopy rPforVMMultipleTestCopy = new RPforVMMultipleCGTestCopy(Logger, DataProvider, ResiliencyReadyWorkflowScheduleLogs);
                CheckPropertyIsNullOrEmpty(server.Properties, "server.Properties is null while Deserialize for GetServerMessage in ", "GetServerMessage");
                bool Result = await rPforVMMultipleTestCopy.AllConsistencyGroup_TestCopy_Scheduler(ResiliencyReadyWorkflowScheduleLogs.ReferenceId, server, Cluster, Wait, TestingNetwork, PoweroncopyVMsduringtesting, BatchCount, ProductionVcenterName, ProductionClusterName, ReplicaVcenterName);
                return actionResultStatus = Result ? "Success" : "Error";
            }
            else if (_workflowAction.ActionName == "RP4VM_MultipleCG_TestCopy")
            {
                _workflowActionReturnResult = await _actionHelper.InsertWorkflowActionResult(actionName, wfActionId, stepId, "Running", false);

                var messageParts = finalScript.Split(new[] { "$@$" }, StringSplitOptions.None);

                string Serverid = messageParts[0].Replace("Result=", "");
                string Cluster = messageParts[1];
                string Wait = messageParts[2];
                string TestingNetwork = messageParts[3];
                string PoweroncopyVMsduringtesting = messageParts[4];
                int BatchCount = messageParts[5].ParseToInt();
                string ProductionVcenterName = messageParts[6];
                string ReplicaVcenterName = messageParts[7];
                string ProductionClusterName = messageParts[8];
                dynamic server = await DataProvider.Server.GetServerById(Serverid);
                RPforVMMultipleCGTestCopy rPforVMMultipleTestCopy = new RPforVMMultipleCGTestCopy(Logger, DataProvider, WorkflowOperationGroup);
                CheckPropertyIsNullOrEmpty(server.Properties, "server.Properties is null while Deserialize for GetServerMessage in ", "GetServerMessage");
                bool Result = await rPforVMMultipleTestCopy.AllConsistencyGroup_TestCopy(server, Cluster, Wait, TestingNetwork, PoweroncopyVMsduringtesting, BatchCount, ProductionVcenterName, ProductionClusterName, ReplicaVcenterName);
                if (Result)
                {
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Success", actionName, wfActionId, "", stepId, false);
                }
                else
                {
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Error", actionName, wfActionId, "", stepId, false);
                }
                var workflowActionResults = await DataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);
                var successStatuses = new HashSet<string> { "success", "skip", "bypassed", "skipped" };
                //var totalSuccessCount = workflowActionResults
                //    .Where(x => successStatuses.Contains(x.Status.ToLower().Trim()))
                //    .ToList();
                var totalSuccessCount = workflowActionResults.Count(x =>
                new[] { "success", "skip", "bypassed", "skipped" }.Contains(x.Status.ToLower().Trim())
            );
                string progressStatus = $"{totalSuccessCount}/{totalActionCount}";

                if (Result)
                {
                    if (totalSuccessCount == totalActionCount)
                    {
                        await _actionHelper.UpdateWorkflowOperationGroupInfra("Completed", progressStatus, actionName, wfActionId, errorMessage);
                        actionResultStatus = ResultStatus.COMPLETED.ToTitleCase();

                    }
                    else
                    {
                        await _actionHelper.UpdateWorkflowOperationGroupInfra("Success", progressStatus, actionName, wfActionId, errorMessage);
                        actionResultStatus = "Success";
                    }
                    //await _actionHelper.UpdateWorkflowOperationGroupInfra("Success", progressStatus, actionName, wfActionId, errorMessage);
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Success", actionName, wfActionId, errorMessage, stepId, false);
                    Logger.Information("CG Test Copy Successfully");
                    return actionResultStatus;
                }
                else
                {
                    await _actionHelper.UpdateWorkflowOperationGroupInfra("Error", progressStatus, actionName, wfActionId, errorMessage);
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Error", actionName, wfActionId, errorMessage, stepId, false);
                    Logger.Information("CG Test Copy is not Executed.");
                    return "Error";
                }
            }

            if (type == ServiceType.CYBER_RESILIENCY && _workflowAction.ActionName == "RP4VM_MultipleCG_TestCopy_StopActivity")
            {
                var messageParts = finalScript.Split(new[] { "$@$" }, StringSplitOptions.None);
                string Serverid = messageParts[0].Replace("Result=", "");
                string Cluster = messageParts[1];
                string Wait = messageParts[2];
                int BatchCount = messageParts[3].ParseToInt();
                string ProductionVcenterName = messageParts[4];
                string ReplicaVcenterName = messageParts[5];
                string ProductionClusterName = messageParts[6];
                dynamic server = await DataProvider.Server.GetServerById(Serverid);
                RPforVMMultipleCGTestCopy rPforVMMultipleTestCopy = new RPforVMMultipleCGTestCopy(Logger, DataProvider, ResiliencyReadyWorkflowScheduleLogs);
                CheckPropertyIsNullOrEmpty(server.Properties, "server.Properties is null while Deserialize for GetServerMessage in ", "GetServerMessage");
                bool Result = await rPforVMMultipleTestCopy.AllConsistencyGroup_TestCopy_StopActivity_Scheduler(server, Cluster, Wait, BatchCount, ProductionVcenterName, ProductionClusterName, ReplicaVcenterName);
                return actionResultStatus = Result ? "Success" : "Error";

            }
            else if (_workflowAction.ActionName == "RP4VM_MultipleCG_TestCopy_StopActivity")
            {
                _workflowActionReturnResult = await _actionHelper.InsertWorkflowActionResult(actionName, wfActionId, stepId, "Running", false);

                var messageParts = finalScript.Split(new[] { "$@$" }, StringSplitOptions.None);
                string Serverid = messageParts[0].Replace("Result=", "");
                string Cluster = messageParts[1];
                string Wait = messageParts[2];
                int BatchCount = messageParts[3].ParseToInt();
                string ProductionVcenterName = messageParts[4];
                string ReplicaVcenterName = messageParts[5];
                string ProductionClusterName = messageParts[6];
                dynamic server = await DataProvider.Server.GetServerById(Serverid);
                RPforVMMultipleCGTestCopy rPforVMMultipleTestCopy = new RPforVMMultipleCGTestCopy(Logger, DataProvider, WorkflowOperationGroup);
                CheckPropertyIsNullOrEmpty(server.Properties, "server.Properties is null while Deserialize for GetServerMessage in ", "GetServerMessage");
                bool Result = await rPforVMMultipleTestCopy.AllConsistencyGroup_TestCopy_StopActivity(server, Cluster, Wait, BatchCount, ProductionVcenterName, ProductionClusterName, ReplicaVcenterName);
                if (Result)
                {
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Success", actionName, wfActionId, "", stepId, false);
                }
                else
                {
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Error", actionName, wfActionId, "", stepId, false);
                }
                var workflowActionResults = await DataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);
                var successStatuses = new HashSet<string> { "success", "skip", "bypassed", "skipped" };
                var totalSuccessCount = workflowActionResults.Count(x =>
                new[] { "success", "skip", "bypassed", "skipped" }.Contains(x.Status.ToLower().Trim()));
                string progressStatus = $"{totalSuccessCount}/{totalActionCount}";

                if (Result)
                {
                    if (totalSuccessCount == totalActionCount)
                    {
                        await _actionHelper.UpdateWorkflowOperationGroupInfra("Completed", progressStatus, actionName, wfActionId, errorMessage);
                        actionResultStatus = ResultStatus.COMPLETED.ToTitleCase();

                    }
                    else
                    {
                        await _actionHelper.UpdateWorkflowOperationGroupInfra("Success", progressStatus, actionName, wfActionId, errorMessage);
                        actionResultStatus = "Success";
                    }
                    //await _actionHelper.UpdateWorkflowOperationGroupInfra("Success", progressStatus, actionName, wfActionId, errorMessage);
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Success", actionName, wfActionId, errorMessage, stepId, false);


                    Logger.Information("CG Test Copy Stopped Successfully");
                    return actionResultStatus;
                }
                else
                {
                    await _actionHelper.UpdateWorkflowOperationGroupInfra("Error", progressStatus, actionName, wfActionId, errorMessage);
                    await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Error", actionName, wfActionId, errorMessage, stepId, false);


                    Logger.Information("CG Test Copy is not Stopped.");
                    return "Error";
                }

            }
            if (_workflowAction.ActionName == "FetchSnaps")
            {

                await _actionHelper.ExecuteFetchSnapsAsync(actionName, wfActionId, stepId, totalActionCount);

                var groupWorkflowListNew = await DataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(WorkflowOperationGroup.ReferenceId);

                var snapProperties = groupWorkflowListNew.SnapProperties;

                Logger.Information("snapProperties:" + snapProperties);


                if (finalScript.Contains("@gv@"))
                {
                    string pattern = @"(?<="").+(?="")";
                    string variableName = string.Empty;
                    var match = System.Text.RegularExpressions.Regex.Matches(finalScript, pattern);
                    foreach (var item in match)
                    {
                        if (!string.IsNullOrEmpty(item.ToString()))
                        {
                            variableName = item.ToString();
                        }

                    }
                    var res = variableName.Split(",");


                    var deserializeJson = JsonConvert.DeserializeObject<dynamic>(snapProperties);
                    var name = deserializeJson.name;
                    var genId = deserializeJson.gen;
                    VariableClient vc = new VariableClient();
                    var res1 = vc.CreateVariable(res[0], name.GetType(), name);
                    Logger.Information("SnapVariable Name:" + res[0]);
                    Logger.Information("SnapVariable Name Value:" + name);
                    var res2 = vc.CreateVariable(res[1], name.GetType(), genId);
                    Logger.Information("SnapVariable Gen:" + res[0]);
                    Logger.Information("SnapVariable Gen Value:" + genId);

                    Logger.Information("Selected Snap Name:" + name);



                }
                return "Success";
            }

            if (type is ServiceType.MONITOR_SERVICE or ServiceType.REPLICATION_SERVICE)
            {
                finalScript = await SubstituteInfraValues(finalScript, _infraId);
            }
            if (finalScript!.Contains("guiautomation"))
            {
                finalScript = finalScript.Replace("guiactionname", actionName);
                finalScript = Regex.Replace(finalScript, @"""@@(\w+)@@""", match =>
                {
                    string variable = match.Groups[1].Value;
                    return variable;
                });
            }
            else
            {
                if (!finalScript.Contains("dll.execute") && !type.Equals(ServiceType.MONITOR_SERVICE))
                {
                    finalScript = finalScript.Replace("\"@@", "").Replace("@@\"", "");
                }
            }

            var script = finalScript;

            Logger.Debug("\r\n" + "************************** Script starts: ***************************** " +
                         "\r\n" + script + "\r\n" +
                         " ************************** ScriptEnd *****************************");

            try
            {
                await InitializeServiceType(type, actionResultStatus, wfActionId, actionName, stepId);

                
                actionResultStatus = await ProcessWorkflowAction(script);
                if (_workflowAction.ActionName == "GetSnapWithStorageGroupAndSID")
                {
                    await _actionHelper.UpdateCyberSnaps("", actionResultStatus);
                    actionResultStatus = "Success";
                }
                if (_workflowAction.ActionName.Contains("UpdateMonitoring_AirGap", StringComparison.OrdinalIgnoreCase))
                {
                    actionResultStatus = await UpdateStatusFromScript(actionResultStatus, UpdateMonitoringCyberAirGapStatus);
                    actionResultStatus = "Success";
                }
                if (_workflowAction.ActionName == "MessageBox")
                {
                    actionResultStatus = await UpdateMessageBox(totalActionCount, stepId, actionName, errorMessage, actionResultStatus, wfActionId);
                    return actionResultStatus;
                }
                if (type is ServiceType.CYBER_RESILIENCY) await UpdateServiceStatus(type, actionResultStatus, errorMessage, _infraId, command);

                if (type is ServiceType.MONITOR_SERVICE or ServiceType.REPLICATION_SERVICE or ServiceType.RESILIENCE_READY_SERVICE)
                {
                    await UpdateServiceStatus(type, actionResultStatus, errorMessage, _infraId, command);
                    if (InfraObject != null)
                    {
                        var workflow = InfraObject.DROperationStatus == 2 ?
                        await DataProvider.Workflow.GetWorkflowById(InfraobjectScheduler.AfterSwitchOverWorkflowId) :
                        await DataProvider.Workflow.GetWorkflowById(InfraobjectScheduler.BeforeSwitchOverWorkflowId);

                        if (workflow is null) return actionResultStatus;

                        var workflowInfraObject = await DataProvider.WorkflowInfraObject.GetWorkflowByInfraIdAndWorkflowId(InfraObject.ReferenceId, workflow.ReferenceId);

                        //Cyber resiliency
                        if (workflowInfraObject != null && workflowInfraObject.ActionType.Replace(" ", "").Equals("CyberResiliency", StringComparison.OrdinalIgnoreCase))
                        {

                            if (!actionResultStatus.Equals("success", StringComparison.OrdinalIgnoreCase)) return actionResultStatus;
                            if (!finalScript.Contains("@gv@") && !finalScript.Contains("gv@@")) return actionResultStatus;

                            string variableName = GetResultString(finalScript, "\"VariableName\":\"", "\",\"VariableValue\"");
                            VariableClient vc = new VariableClient();
                            var output = vc.GetValue(variableName);

                            await UpdateCyberSnaps(actionResultStatus, output.ToString()!);
                            return actionResultStatus;
                        }
                    }
                }
                if (mode.Equals(ExecutionMode.SIMULATE.ToLower()))
                {
                    actionResultStatus = ResultStatus.SUCCESS.ToTitleCase();
                }
            }
            catch (Exception exc)
            {
                Logger.Exception("Exception occurred while process workflow action.", exc);

                (actionResultStatus, errorMessage) = HandleException(exc, actionResultStatus, errorMessage);

                if (type is ServiceType.RESILIENCE_READY_SERVICE or ServiceType.CYBER_RESILIENCY)
                {
                    await _drReadiness.ResiliencyReadyStatusAndLogUpdate(actionResultStatus, errorMessage, command);
                }

                if (type.Equals(ServiceType.CYBER_AIRGAP))
                {
                    var airGap = await DataProvider.CyberAirGap.GetCyberAirGapById(CyberAirGap.ReferenceId);
                    if (airGap != null)
                    {
                        airGap.WorkflowStatus = "Error";
                        airGap.ErrorMessage = exc.Message;
                    }

                    await DataProvider.CyberAirGap.UpdatecyberAirGapAsync(airGap);
                }

                actionResultStatus = string.IsNullOrEmpty(actionResultStatus) ? ResultStatus.ERROR.ToTitleCase() : actionResultStatus;

                if (type == ServiceType.MONITOR_SERVICE)
                {
                    await _monitorService.HandleStatusUpdatesAndLogging(actionResultStatus, errorMessage, _infraId);
                }

            }

            Logger.Information($"Executed Action Result is: {actionResultStatus}");

            actionResultStatus = string.IsNullOrEmpty(actionResultStatus) ? ResultStatus.ERROR.ToTitleCase() : actionResultStatus;

            if (type is ServiceType.MONITOR_SERVICE or ServiceType.REPLICATION_SERVICE)
            {
                Logger.Information("RunSequentialAction-Monitor service action ended.");
                actionResultStatus = "completed";
                return actionResultStatus;
            }

            if(actionResultStatus!= "Success")
            {
                actionResultStatus = "Error";
            }
            if (type == ServiceType.WORKFLOW_SERVICE)
            {
                await _actionHelper.UpdateWorkflowActionResultStatus(
                    _workflowActionReturnResult.workflowActionResultId, actionResultStatus, actionName, wfActionId, errorMessage, stepId, false);

                Logger.Debug($"RunActionEvents-ActionResultStatus {actionResultStatus}");

                var workflowActionResults = await DataProvider.WorkflowActionResult
                    .GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);

                Logger.Debug($"RunSequentialAction-Retrieved WorkflowOperationGroupId {WorkflowOperationGroup.WorkflowName}");

                // Define success status keywords in a HashSet for efficient checking
                var successStatuses = new HashSet<string> { "success", "skip", "bypassed", "skipped" };
                var totalSuccessCount = workflowActionResults
                    .Where(x => successStatuses.Contains(x.Status.ToLower().Trim()))
                    .ToList();

                Logger.Debug($"RunSequentialAction-WorkflowActionResult count with Success or Skip: {totalSuccessCount.Count}");

                string progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";

                if (totalSuccessCount.Count == totalActionCount)
                {
                    Logger.Debug($"RunSequentialAction-TotalSuccessCount {totalSuccessCount.Count} equals totalActionCount {totalActionCount}");

                    if (actionResultStatus != ResultStatus.ERROR.ToTitleCase())
                    {
                        Logger.Debug("RunSequentialAction-actionResultStatus is not Error");
                        actionResultStatus = ResultStatus.COMPLETED.ToTitleCase();
                    }
                }

                await _actionHelper.UpdateWorkflowOperationGroupInfra(actionResultStatus, progressStatus, actionName, wfActionId, errorMessage);

                Logger.Debug($"RunSequentialAction-Updated ActionResultStatus {actionResultStatus}, ProgressStatus {progressStatus}, actionName {actionName}");
            }

            Logger.Debug("RunAction method ended");
        }
        catch (Exception exc)
        {
            errorMessage = exc.Message;
            Logger.Error($"Exception occurring in RunSequentialAction: {exc.Message}");

            var workflowActionResults = await DataProvider.WorkflowActionResult
                .GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);

            // Define a HashSet for success-related statuses for easy checking and maintainability
            var successStatuses = new HashSet<string> { "success", "skip", "bypassed", "skipped" };
            var totalSuccessCount = workflowActionResults
                .Count(x => successStatuses.Contains(x.Status.ToLower().Trim()));

            Logger.Debug($"RunSequentialAction - total WorkflowActionResult count with Success or Skip: {totalSuccessCount}");

            string progressStatus = $"{totalSuccessCount}/{totalActionCount}";

            // Extract wfActionId directly from actionInfo for clarity
            var wfActionId = actionInfo.SelectToken("actionType")?.Value ?? "UnknownActionType";
            await _actionHelper.UpdateWorkflowOperationGroupInfra("Error", progressStatus, actionName, wfActionId, errorMessage);
            await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, actionResultStatus, actionName, wfActionId, "", stepId, false);

            // Update actionResultStatus to a standardized error status
            actionResultStatus = ResultStatus.ERROR.ToTitleCase();

        }

        return actionResultStatus;
    }

    public async Task<string> UpdateCyberSnaps(string status, string result)
    {
        try
        {
            Logger.Information($"Monitor UpdateCyberSnap method started.");

            string sgName, sId, snapName, linkTime = string.Empty, gen, secureStatus, unLinkTime = string.Empty;

            sgName = GetResultString(result, "(SG) Name", "SG's").Replace(":", "").Trim();
            sId = GetResultString(result, "Symmetrix ID", "(Microcode Version").Replace(":", "").Trim();
            sId = sId.Length >= 4 ? sId[^4..] : sId;

            var output = GetResultString(result, "Date", "Flags:").Trim();
            var splitValue = output.Split("\n");

            foreach (var value in splitValue)
            {
                var spaceSplit = value.Split("  ").Where(s => !string.IsNullOrWhiteSpace(s)).Distinct().ToList();
                if (spaceSplit.Count <= 2) continue;

                snapName = spaceSplit[0].Split(" ").Length > 1 ? spaceSplit[0].Split(" ")[1] : spaceSplit[0].Split(" ")[0];
                var linkValue = spaceSplit[1].Split(" ")[2][1];
                var linkSG = linkValue == 'X';

                var secureValue = spaceSplit[1].Split(" ")[3][1];
                secureStatus = (secureValue == 'X').ToString();
                gen = spaceSplit[1].Trim().Split(" ")[0].Trim();

                var dateTimeMatch = Regex.Match(value, @"(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun)\s+\w{3}\s+\d{1,2}\s+\d{2}[:]\d{2}[:]\d{2}\s+\d{4}");
                var time = dateTimeMatch.Success ? dateTimeMatch.Value : string.Empty;

                if (linkSG)
                {
                    linkTime = time;
                }
                else
                {
                    unLinkTime = time;
                }

                var snap = await DataProvider.CyberSnapService.GetCyberSnapBySnapShotNameAndSGNameAndGen(snapName, sgName, gen);
                if (snap is not null)
                {
                    snap.Name = snapName;
                    snap.SnapshotName = snapName;
                    snap.StorageGroupName = sgName;
                    snap.LinkSG = linkSG.ToString();
                    snap.TimeStamp = time;
                    //snap.LinkedStatus = linkSG;
                    //snap.LinkedSGTime = linkTime;
                    //snap.UnlinkSGTime = unLinkTime;
                    snap.SID = sId;
                    snap.Gen = gen;
                    snap.SecureStatus = secureStatus;

                    await DataProvider.CyberSnapService.UpdatecyberSnapAsync(snap);
                }
                else
                {
                    await DataProvider.CyberSnapService.CreateCyberSnapStatus(new CyberSnaps
                    {
                        SID = sId,
                        Name = snapName,
                        SnapshotName = snapName,
                        LinkedStatus = linkSG ? "Linked" : "-",
                        StorageGroupName = sgName,
                        LinkSG = linkSG.ToString(),
                        TimeStamp = time,
                        Gen = gen,
                        SecureStatus = secureStatus,
                        //LinkedSGTime = linkTime,
                        //UnlinkSGTime = unLinkTime
                    });
                }
            }

            Logger.Debug($"Monitor UpdateCyberSnap method ended.");
            return status;
        }
        catch (Exception ex)
        {
            Logger.Exception("Exception occurred while performing UpdateCyberSnap method", ex);
            throw;
        }
    }

    private string GetResultString(string result, string start, string end)
    {
        int startIndex = result.IndexOf(start, StringComparison.Ordinal) + start.Length;
        int endIndex = result.IndexOf(end, startIndex, StringComparison.Ordinal);
        return result[startIndex..endIndex];
    }

    private (string actionResultStatus, string errorMessage) HandleException(Exception exc, string actionResultStatus, string errorMessage)
    {
        if (exc.Message.Contains("$@$"))
        {
            var messageParts = exc.Message.Split(new[] { "$@$" }, StringSplitOptions.None);
            //actionResultStatus = messageParts[0];
            var matchedOutput = messageParts[1];
            Logger.Error($"Output Not Matched With: {matchedOutput}");
        }

        actionResultStatus = "Error";
        Logger.Error($"Exception occurring in Executed Action {actionResultStatus}");
        errorMessage = exc.Message;

        return (actionResultStatus, errorMessage);
    }
    private async Task<string> UpdateMonitoringCyberAirGapStatus(string airGapId, string status)
    {
        string resultStatus = string.Empty;
        try
        {
            status = status.Equals("disable", StringComparison.OrdinalIgnoreCase) ? "Lock" : "Unlock";

            var airGapList = await DataProvider.CyberAirGap.GetCyberAirGapById(airGapId);
            if (airGapList != null)
            {
                var exceptionType = status.Equals("Unlock", StringComparison.OrdinalIgnoreCase) ? ExceptionType.CyberAirGap_Status_Open : ExceptionType.CyberAirGap_Status_Close;
                var userMessage = $"AirGap '{airGapList.Name}' is in '{status}' state.";

                var airGapStatus = await DataProvider.CyberAirGapStatusService.GetCyberAirGapStatusByAirGapId(airGapList.ReferenceId);
                if (airGapStatus != null)
                {
                    if (!status.Equals(airGapStatus.Status, StringComparison.OrdinalIgnoreCase))
                    {
                        resultStatus = await MonitoringAirGapAsync(status, airGapList);
                        await CyberAirGapAlerts(new ContinuityPatrolException(userMessage, userMessage, exceptionType, "AirGap"), airGapList);
                    }
                }
                else
                {
                    resultStatus = await MonitoringAirGapAsync(status, airGapList);
                    await CyberAirGapAlerts(new ContinuityPatrolException(userMessage, userMessage, exceptionType, "AirGap"), airGapList);
                }
            }
            else
            {
                Logger.Information("AirGapList throws Empty Data");
                resultStatus = "Error";
            }
        }
        catch (Exception exc)
        {
            resultStatus = "Error";
            var airGapList = await DataProvider.CyberAirGap.GetCyberAirGapById(airGapId);
            if (airGapList != null)
            {
                airGapList.WorkflowStatus = resultStatus;
                airGapList.ErrorMessage = exc.Message;
                await DataProvider.CyberAirGap.UpdatecyberAirGapAsync(airGapList);
            }

            Logger.Exception("UpdateDrOperationStatus throws error.", exc);
        }
        return resultStatus;
    }
    private async Task<string> UpdateMessageBox(int totalActionCount, string stepId, string actionName, string errorMessage, string actionResultStatus, string wfActionId)
    {
        try
        {
            var successStatuses = new HashSet<string> { "success", "skip", "bypassed", "skipped" };
            await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "WaitMessage", actionName, wfActionId, errorMessage, stepId, false);

            CheckPropertyIsNullOrEmpty(WorkflowOperationGroup.ReferenceId, "(WorkflowOperationGroup.ReferenceId is null while retrieve workflowActionResults for update message in ", "UpdateMessageBox");
            var workflowActionResults1 = await DataProvider.WorkflowActionResult
                .GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);

            var totalSuccessCount1 = workflowActionResults1
                .Where(x => successStatuses.Contains(x.Status.ToLower().Trim()))
                .ToList();

            Logger.Debug($"RunSequentialAction-WorkflowActionResult count with Success or Skip: {totalSuccessCount1.Count}");

            string progressStatus = $"{totalSuccessCount1.Count}/{totalActionCount}";



            await _actionHelper.UpdateWorkflowOperationGroupInfra("WaitMessage", progressStatus, actionName, wfActionId, actionResultStatus);


            await _actionHelper.DisplayMessage();

            await _actionHelper.UpdateWorkflowActionResultStatus(_workflowActionReturnResult.workflowActionResultId, "Success", actionName, wfActionId, errorMessage, stepId, false);


            Logger.Information("MessageBox Result:" + actionResultStatus);

            var workflowActionResults = await DataProvider.WorkflowActionResult
                .GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);

            Logger.Debug($"RunSequentialAction-Retrieved WorkflowOperationGroupId {WorkflowOperationGroup.WorkflowName}");

            // Define success status keywords in a HashSet for efficient checking

            var totalSuccessCount = workflowActionResults
                .Where(x => successStatuses.Contains(x.Status.ToLower().Trim()))
                .ToList();

            Logger.Debug($"RunSequentialAction-WorkflowActionResult count with Success or Skip: {totalSuccessCount.Count}");

            progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";


            if (totalSuccessCount.Count == totalActionCount)
            {
                Logger.Information($"RunSequentialAction-MessageBox count  is matched and update the status as completed:" + progressStatus);
                await _actionHelper.UpdateWorkflowOperationGroupInfra("Completed", progressStatus, actionName, wfActionId, actionResultStatus);
            }
            else
            {
                await _actionHelper.UpdateWorkflowOperationGroupInfra("Success", progressStatus, actionName, wfActionId, actionResultStatus);
            }

            return "Success";
        }
        catch (Exception)
        {

            return "Error";
        }
    }

    private async Task<string> ProcessWorkflowAction(string script)
    {
        string resultStatus;

        CheckPropertyIsNullOrEmpty(_workflowAction.ActionName.ToLower(), "_workflowAction.ActionName is null while ProcessWorkflowAction in ", "ProcessWorkflowAction");
        switch (_workflowAction.ActionName.ToLower())
        {
            case "updateworkflowoperationstatus":
                var variablesplitvalue = script.Split("=")[0];
                var splitResult = script.Split("=")[1].Split("@$@");
                var infraId = splitResult[0].TrimEnd();
                var drOperationStatus = splitResult[1].TrimEnd(';');
                var variableName = splitResult[2].TrimEnd(';');
                VariableClient vc = new VariableClient();
                var res1 = vc.CreateVariable(variablesplitvalue, variablesplitvalue.GetType(), drOperationStatus);
                resultStatus = await UpdateDrOperationStatus(infraId, drOperationStatus);
                break;
            case "updatesnaplinkstatus":
                var splitResult1 = script.Split("=")[1].Split("@$@");
                var SnapName = splitResult1[0].TrimEnd();
                var gen = splitResult1[1].TrimEnd(';');
                var status = splitResult1[2].TrimEnd(';');
                resultStatus = await UpdateSnapLinkStatus(SnapName, gen, status);
                break;
            case string actionName when actionName.Contains("updateairgap", StringComparison.OrdinalIgnoreCase):
                resultStatus = await UpdateStatusFromScript(script, UpdateCyberAirGapStatus);
                break;
            case string actionName when actionName.Contains("updatesnap", StringComparison.OrdinalIgnoreCase):
                resultStatus = await UpdateStatusFromScript(script, UpdateSnapStatus);
                break;

            case "executecpl":

                script = await RunActionScript(script);

                resultStatus = await RunActionScript(script);
                break;

            case string actionName when actionName is not "waitforparallel" and not "waitforworkflowaction":
                resultStatus = await ExecuteNonParallelAction(script);
                break;

            default:
                resultStatus = "Success";
                break;
        }

        return resultStatus;
    }
    public async Task<string> UpdateDrOperationStatus(string infraId, string drOperationStatus)
    {
        string resultStatus = "Error"; // Default to "Error" unless successful
        try
        {
            Logger.Information("Starting UpdateDrOperationStatus for Infra ID: {0} with DR Operation Status: {1}", infraId, drOperationStatus);

            int status = drOperationStatus.ToLower() switch
            {
                "switchover" => 2,
                "switchback" => 5,
                "failover" => 8,
                "failback" => 11,
                "custom" => 14,
                _ => 0
            };

            if (status == 0)
            {
                Logger.Warning("Unrecognized DR Operation Status: {0}. No status update will be made.", drOperationStatus);
                return resultStatus;
            }

            var infraObject = await DataProvider.InfraObject.GetInfraObjectById(infraId);

            if (infraObject != null)
            {
                infraObject.DROperationStatus = status;

                Logger.Information("Updating DR Operation Status to {0} for InfraObject: {1}", status, infraObject.Name);
                await DataProvider.InfraObject.UpdateInfraObject(infraObject);

                Logger.Information("Successfully updated DR Operation Status for InfraObject: {0} with new status code: {1}", infraObject.Name, status);
                resultStatus = "Success";
            }
            else
            {
                Logger.Error("InfraObject with ID {0} not found.", infraId);
            }
        }
        catch (Exception exc)
        {
            Logger.Error("Exception occurred in UpdateDrOperationStatus for Infra ID {0}. Error: {1}", infraId, exc.Message);
            throw;
        }

        return resultStatus;
    }
    private async Task<string> UpdateSnapLinkStatus(string SnapName, string gen, string status)
    {
        Logger.Information("UpdateSnapLinkStatus SnapName:" + SnapName);
        Logger.Information("UpdateSnapLinkStatus gen:" + gen);
        Logger.Information("UpdateSnapLinkStatus status:" + status);
        string resultStatus;
        try
        {
            var snapList = await DataProvider.CyberSnap.GetCyberSnapByName(SnapName, gen);
            if (snapList != null)
            {

                if (status == "Linked")
                {
                    snapList.LinkedStatus = "Linked";
                    snapList.LinkedSGTime = Convert.ToString(DateTime.Now);
                }
                else
                {
                    snapList.LinkedStatus = "-";
                    snapList.UnlinkSGTime = Convert.ToString(DateTime.Now);
                }

                await DataProvider.CyberSnap.UpdatecyberSnapAsync(snapList);

                Logger.Information("UpdateSnapLinkStatus status Linked");
                resultStatus = "Success";
            }
            else
            {
                Logger.Information("UpdateSnapLinkStatus is null");
                resultStatus = "Error";
            }

        }
        catch (Exception exc)
        {
            Logger.Error("UpdateSnapLinkStatus throws exception:" + exc.Message);
            resultStatus = "Error";
        }
        return resultStatus;
    }
    private async Task<string> UpdateStatusFromScript(string script, Func<string, string, Task<string>> updateStatusFunc)
    {
        var splitResult = script.Split("=")[1].Split("@$@");
        var id = splitResult[0].TrimEnd();
        var status = splitResult[1].TrimEnd(';');
        return await updateStatusFunc(id, status.Trim());
    }

    public async Task<string> ExecuteNonParallelAction(string script)
    {
        string resultStatus = await RunActionScript(script);

        if (resultStatus.Contains("$@$"))
        {
            resultStatus = resultStatus.Replace("$_$", "-");
            var matchedOutput = resultStatus.Split(new[] { "$@$" }, StringSplitOptions.None)[1];
            resultStatus = resultStatus.Split(new[] { "$@$" }, StringSplitOptions.None)[0];
            Logger.Information($"Executed Action Result split with : {resultStatus}");
            Logger.Information($"Output Matched With: {matchedOutput}");
        }
        return resultStatus;
    }

    private async Task UpdateServiceStatus(ServiceType type, string actionResultStatus, string errorMessage, string infraId, string command)
    {
        switch (type)
        {
            case ServiceType.MONITOR_SERVICE:
                await _monitorService.HandleStatusUpdatesAndLogging(actionResultStatus, errorMessage, infraId);
                break;

            case ServiceType.REPLICATION_SERVICE:
                await _replicationService.UpdateReplicationJobStatusAndLog(actionResultStatus, infraId, errorMessage);
                break;

            case ServiceType.RESILIENCE_READY_SERVICE:
                await _drReadiness.ResiliencyReadyStatusAndLogUpdate(actionResultStatus, errorMessage, command);
                break;
        }
    }

    private async Task InitializeServiceType(ServiceType type, string actionResultStatus, string wfActionId, string actionName, string stepId)
    {
        switch (type)
        {
            case ServiceType.WORKFLOW_SERVICE:
                _workflowActionReturnResult = await _actionHelper.InsertWorkflowActionResult(actionName, wfActionId, stepId, OperationStatus.RUNNING.ToTitleCase(), false);
                break;

            case ServiceType.CYBER_RESILIENCY:
            case ServiceType.RESILIENCE_READY_SERVICE:
                _drReadiness = new DRReadiness(InfraObject, Logger, DataProvider, InfraobjectScheduler, ServiceProvider, actionResultStatus, wfActionId, actionName);
                break;

            case ServiceType.MONITOR_SERVICE:
                _monitorService = new MonitorService(Logger, DataProvider, Job);
                break;

            case ServiceType.REPLICATION_SERVICE:
                _replicationService = new ReplicationService(Logger, DataProvider, ReplicationJob);
                break;
        }
    }

    private string UpdateFinalScript(string script, dynamic actionProperties, ServiceType serviceType)
    {
        //foreach (var property in actionProperties)
        //{
        //    script = script.Replace(property.Name.ToString(), property.Value.ToString());
        //}
        //return script;
        foreach (var property in actionProperties)
        {
            script = script.Replace(property.Name.ToString(), property.Value.ToString()); var checkScript = script.Split("\n");
            CheckPropertyIsNullOrEmpty(property.Name, "actionProperties.Name is null while UpdateFinalScript in ", "UpdateFinalScript");
            CheckPropertyIsNullOrEmpty(property.Value, "actionProperties.Value is null while UpdateFinalScript in ", "UpdateFinalScript");
            string _paraval = property.Name.ToString();
            string oldValue = property.Value.ToString();
            if (checkScript.Length > 0)
            {
                foreach (var cs in checkScript)
                {
                    if (cs.Contains("dll.execute"))
                    {
                        //var newValue1 = oldValue.Replace("+", "$plus$").Replace("/", "$forwardslash$").Replace("-", "$hypen$").Replace(">", "$greater$").Replace("=", "$equal$").Replace(" ", "$@$").Replace(":", "$cln$");
                        //if (oldValue != "")
                        //{
                        //    var tes = cs.Replace(oldValue, newValue1);
                        //    if (script.Contains("//"))
                        //    {
                        //        script = script.Replace("://", "$#$");
                        //    }

                        //    //script = script.Replace("/", "$forwardslash$");
                        //    script = script.Replace(cs, tes);
                        //}
                        var newValue1 = oldValue.Replace("+", "$Plus$").Replace("-", "$hypen$").Replace(">", "$greater$").Replace("=", "$equals$").Replace(":", "$cln$").Replace(".", "$dot$").Replace(" ", "$Space$").Replace("_", "$Uscore$").Replace("*", "$Star$").Replace(",", "$Comma$");
                        if (oldValue != "")
                        {
                            var tes = cs.Replace(oldValue, newValue1);
                            if (script.Contains("//"))
                            {
                                script = script.Replace("://", "$#$");
                            }

                            //finalScript = finalScript.Replace("/", "$$");

                            script = script.Replace(cs, tes);
                            script = script.Replace("/", "$Slash$");
                        }
                    }
                    else if (cs.Contains("powershell.commands"))
                    {
                        if (!_paraval.Contains("CheckOutput"))
                        {
                            //var newValue1 = oldValue.Replace("-", "$_$");
                            // var tes = cs.Replace(oldValue, newValue1);
                            // script = script.Replace(oldValue, newValue1);
                        }
                    }
                    else
                    {
                        script = script.Replace("?", "$conditionaloperator$");
                        //script = script.Replace("?", "$conditionaloperator$").Replace("-", "$_$");
                    }
                }
            }
        }
        try
        {
            var GetAllvariables = VariableStore.Instance._store;
            foreach (var item in GetAllvariables)
            {
                var VariableName = item.Key.ToString();

                if (string.IsNullOrEmpty(VariableName))
                {
                    continue;
                }
                VariableClient vc = new VariableClient();
                var VariableValue = vc.GetValue(VariableName).ToString();

                script = script.Replace(VariableName, VariableValue);

            }

        }
        catch (Exception)
        {


        }
        return script;
    }

    private async Task<string> BuildMessageForAction(dynamic workflowActionProperties)
    {
        var messageBuilder = new StringBuilder();

        foreach (var property in workflowActionProperties)
        {
            CheckPropertyIsNullOrEmpty(property.Name, "actionProperties.Name is null while BuildMessageForAction in ", "BuildMessageForAction");
            CheckPropertyIsNullOrEmpty(property.Value, "actionProperties.Value is null while BuildMessageForAction in ", "BuildMessageForAction");
            string name = property.Name.ToString();
            string value = property.Value.ToString();

            if (name.Contains("Server"))
            {
                messageBuilder.Append(await GetServerMessage(value));
            }
            else if (name.Contains("DBName"))
            {
                messageBuilder.Append(await GetDatabaseMessage(value));
            }
            else if (name.Contains("replication"))
            {
                var replication = await DataProvider.Replication.GetReplicationById(value);
                messageBuilder.Append($"ReplicationName:{replication.Name},,");
            }
            else if (name.Contains("ScriptBlock"))
            {
                var scriptBlock = SecurityHelper.Decrypt(value.Replace("_encryptedcpl", ""));

                messageBuilder.Append($"{name.Replace("@@", "")}:{Environment.NewLine}{scriptBlock},,");
            }
            else
            {
                messageBuilder.Append(GetDefaultMessage(name, value));
            }
        }

        return messageBuilder.ToString().TrimEnd(',');
    }

    private string GetDefaultMessage(string name, string value)
    {
        string processedValue = name.ToLower().Contains("password") ? "*********" : value;

        if (value.Contains("_encryptedcpl"))
        {
            processedValue = SecurityHelper.Decrypt(value.Replace("_encryptedcpl", ""));
        }

        return $"{name.Replace("@@", "")}:{Environment.NewLine}{processedValue},,";
    }

    private async Task<string> GetServerMessage(string serverId)
    {
        var server = await DataProvider.Server.GetServerById(serverId);
        CheckPropertyIsNullOrEmpty(server.Properties, "server.Properties is null while Deserialize for GetServerMessage in ", "GetServerMessage");
        var properties = JsonConvert.DeserializeObject<dynamic>(server.Properties);

        bool useHostName = properties?.ConnectViaHostName;

        var connectionType = useHostName ? "Host Name" : "IP Address";

        var prefix = server.ServerType.StartsWith("PR") ? "PR" : "DR";

        string connectionDetail = (connectionType == "Host Name" ? properties?.HostName : properties?.IpAddress)!;

        return $"{prefix}{connectionType}:{connectionDetail},,";
    }

    private async Task<string> GetDatabaseMessage(string databaseId)
    {
        CheckPropertyIsNullOrEmpty(databaseId, "databaseId is null while retrieve database based on Id in ", "GetDatabaseMessage");
        var database = await DataProvider.Database.GetDatabaseById(databaseId);
        CheckPropertyIsNullOrEmpty(database.Properties, "database.Properties is null while GetJsonDatabaseSidValue in ", "GetDatabaseMessage");
        string sidValue = GetJsonDatabaseSidValue(database.Properties);

        string prefix = database.Type.StartsWith("PR") ? "PR" : "DR";
        return $"{prefix}DatabaseSID:{sidValue},,";
    }

    private async Task<string> SubstituteInfraValues(string finalScript, string infraId)
    {
        var infra = await DataProvider.InfraObject.GetInfraObjectById(infraId);
        (string prServerId, string drServerId) = PropertyValueModifier.GetPrAndDrIds(infra.ServerProperties);
        (string prDbId, string drDbId) = PropertyValueModifier.GetPrAndDrIds(infra.DatabaseProperties);
        (string prReplicationId, string drReplicationId) = PropertyValueModifier.GetPrAndDrIds(infra.ReplicationProperties);

        //CheckPropertyIsNullOrEmpty(drServerId, "infra.DRServerId is null while finalscript process in ", "SubstituteInfraValues");
        //CheckPropertyIsNullOrEmpty(prServerId, "infra.PRServerId is null while finalscript process in ", "SubstituteInfraValues");
        //CheckPropertyIsNullOrEmpty(drDbId, "infra.DRDatabaseId is null while finalscript process in ", "SubstituteInfraValues");
        //CheckPropertyIsNullOrEmpty(prDbId, "infra.PRDatabaseId is null while finalscript process in ", "SubstituteInfraValues");
        //CheckPropertyIsNullOrEmpty(drReplicationId, "infra.DRReplicationId is null while finalscript process in ", "SubstituteInfraValues");
        //CheckPropertyIsNullOrEmpty(prReplicationId, "infra.PRReplicationId is null while finalscript process in ", "SubstituteInfraValues");
        finalScript = infra.DROperationStatus == (int)DROperationStatus.SWITCH_OVER_COMPLETED
            ? ReplaceInfraValues(finalScript, drServerId, prServerId, drDbId, prDbId, drReplicationId, prReplicationId)
            : ReplaceInfraValues(finalScript, prServerId, drServerId, prDbId, drDbId, prReplicationId, drReplicationId);

        return finalScript;
    }

    private string ReplaceInfraValues(string script, string prServerId, string drServerId, string prDatabaseId, string drDatabaseId, string prReplicationId, string drReplicationId)
    {
        return script
            .Replace("@@PRServerName_key", prServerId).Replace("@@PRServer_key", prServerId).Replace("@@PRServerName", prServerId)
            .Replace("@@DRServerName_key", drServerId).Replace("@@DRServer_key", drServerId).Replace("@@DRServerName", drServerId)
            .Replace("@@PRDBName_key", prDatabaseId).Replace("@@PRDatabase_key", prDatabaseId).Replace("@@PRDBName", prDatabaseId)
            .Replace("@@DRDBName_key", drDatabaseId).Replace("@@DRDatabase_key", drDatabaseId).Replace("@@DRDBName", drDatabaseId)
            .Replace("@@PRReplication_key", prReplicationId).Replace("@@PRReplicationName", prReplicationId).Replace("@@PRReplication", prReplicationId).Replace("@@Replication", prReplicationId)
            .Replace("@@DRReplication_key", drReplicationId).Replace("@@DRReplicationName", drReplicationId).Replace("@@DRReplication", drReplicationId).Replace("@@Replication", prReplicationId);
    }

    private async Task DrOperationType(dynamic workflowActionProperties)
    {
        CheckPropertyIsNullOrEmpty(WorkflowOperationGroup.InfraObjectId, "WorkflowOperationGroup.InfraObjectId is null in ", "DrOperationType");

        var infraList = await DataProvider.InfraObject.GetInfraObjectById(WorkflowOperationGroup.InfraObjectId);

        CheckPropertyIsNullOrEmpty(infraList, "infraList is null in ", "DrOperationType");

        (string prServerId, string drServerId, Dictionary<string, string> customSerIds) = PropertyValueModifier.GetIds(infraList.ServerProperties);
        (string prDbId, string drDbId, Dictionary<string, string> customDbIds) = PropertyValueModifier.GetIds(infraList.DatabaseProperties);

        const string farDrServerId = "";
        const string farDrDatabaseId = "";

        var propsBuilder = new StringBuilder();

        foreach (var actionProperty in workflowActionProperties)
        {
            string name = actionProperty.Name.ToString();
            string value = actionProperty.Value.ToString();

            CheckPropertyIsNullOrEmpty(actionProperty.Name, "actionProperty.Name is null in ", "DrOperationType");
            CheckPropertyIsNullOrEmpty(actionProperty.Value, "actionProperty.Value is null in ", "DrOperationType");

            if (name.ToLower().Contains("target"))
            {
                if (value.ToLower().Contains("neardr"))
                {
                    propsBuilder.AppendLine("{")
                                .AppendLine("\"PR\": {")
                                .AppendLine($"\"ServerId\": \"{customSerIds.FirstOrDefault()}\",")
                                .AppendLine($"\"DatabaseId\": \"{customDbIds.FirstOrDefault()}\"")
                                .AppendLine("},")
                                .AppendLine("\"NearDR\": {")
                                .AppendLine($"\"ServerId\": \"{prServerId}\",")
                                .AppendLine($"\"DatabaseId\": \"{prDbId}\"")
                                .AppendLine("}")
                                .AppendLine("}");
                }
                else
                {
                    propsBuilder.AppendLine("{")
                                .AppendLine("\"PR\": {")
                                .AppendLine($"\"ServerId\": \"{farDrServerId}\",")
                                .AppendLine($"\"DatabaseId\": \"{farDrDatabaseId}\"")
                                .AppendLine("},")
                                .AppendLine("\"FarDR\": {")
                                .AppendLine($"\"ServerId\": \"{prServerId}\",")
                                .AppendLine($"\"DatabaseId\": \"{prDbId}\"")
                                .AppendLine("}")
                                .AppendLine("}");
                }
                break;
            }
        }

        var props = propsBuilder.ToString();

        // Initialize the operational status
        var st = new InfraOperationalStatus
        {
            InfraObjectId = WorkflowOperationGroup.InfraObjectId,
            Properties = props,
            DROperationStatus = 15
        };

        // Check and update/create InfraOperationalStatus
        var existingInfraStatus = await DataProvider.InfraOperationalStatus.GetInfraOperationalStatusByInfraObjectId(WorkflowOperationGroup.InfraObjectId);

        if (existingInfraStatus != null)
        {
            existingInfraStatus.Properties = props;
            existingInfraStatus.DROperationStatus = 15;
            await DataProvider.InfraOperationalStatus.UpdateInfraOperationalStatus(existingInfraStatus);
        }
        else
        {
            await DataProvider.InfraOperationalStatus.CreateInfraOperationalStatus(st);
        }
    }

    public static string GetJsonDatabaseSidValue(string json)
    {
        json = json.Trim().ToLower();
        JObject jSonObject;

        try
        {
            jSonObject = JObject.Parse(json);
        }
        catch (JsonReaderException)
        {
            return "Invalid JSON format";
        }

        // Define possible keys and attempt to retrieve their values
        var possibleKeys = new List<string> { "databasename", "databasesid", "database", "sid", "oraclesid", "instancename" };
        var foundValue = possibleKeys
            .Where(key => jSonObject.ContainsKey(key))
            .Select(key => jSonObject[key]?.ToString())
            .FirstOrDefault(value => !string.IsNullOrEmpty(value));

        return foundValue ?? "NA";
    }

    private async Task<string> SkipWaitForWorkflowAction(dynamic totalActionCount, string stepId, string actionName, string actionId)
    {
        await _actionHelper.InsertWorkflowActionResult(actionName, actionId, stepId, "Success", false);

        CheckPropertyIsNullOrEmpty(WorkflowOperationGroup.ReferenceId, "WorkflowOperationGroup.ReferenceId is null in ", "SkipWaitForWorkflowAction");

        var workflowActionResults = await DataProvider.WorkflowActionResult
            .GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);

        Logger.Information($"{actionName}: Customized execution - Skipped unchecked action");

        var successfulActions = workflowActionResults
            .Where(x => new[] { "success", "skip", "bypassed" }.Contains(x.Status.ToLower().Trim()))
            .ToList();

        Logger.Information($"Customized execution - Completed action count: {successfulActions.Count}");

        var progressStatus = $"{successfulActions.Count}/{totalActionCount}";
        var status = "Success";

        await _actionHelper.UpdateWorkflowOperationGroupInfra(status, progressStatus, actionName, actionId, "");

        Logger.Debug($"{actionName}: Updated Action Status: {status} | Progress Status: {progressStatus}");

        return status;
    }

    public async Task<string> SkipForWorkflowAction(dynamic totalActionCount, string stepId, string actionName, string actionId)
    {
        await _actionHelper.InsertWorkflowActionResult(actionName, actionId, stepId, "skip", false);

        CheckPropertyIsNullOrEmpty(WorkflowOperationGroup.ReferenceId, "WorkflowOperationGroup.ReferenceId is null in ", "SkipForWorkflowAction");
        var workflowActionResults = await DataProvider.WorkflowActionResult
            .GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);

        Logger.Information($"{actionName}: Customized execution - Skipped unchecked action");

        var successfulActions = workflowActionResults
            .Where(x => new[] { "success", "skip", "bypassed" }.Contains(x.Status.ToLower().Trim()))
            .ToList();

        Logger.Information($"Customized execution - Completed action count: {successfulActions.Count}");

        var progressStatus = $"{successfulActions.Count}/{totalActionCount}";
        var status = "skip";

        await _actionHelper.UpdateWorkflowOperationGroupInfra(status, progressStatus, actionName, actionId, "");

        Logger.Debug($"{actionName}: Updated Action Status: {status} | Progress Status: {progressStatus}");

        return status;
    }

    private async Task<string> SkipDrCustomAction(dynamic totalActionCount, string stepId, string actionName, string actionId)
    {
        await _actionHelper.InsertWorkflowActionResult(actionName, actionId, stepId, "Success", false);

        CheckPropertyIsNullOrEmpty(WorkflowOperationGroup.ReferenceId, "WorkflowOperationGroup.ReferenceId is null in ", "SkipDrCustomAction");

        var workflowActionResults = await DataProvider.WorkflowActionResult
            .GetWorkflowActionResultByWorkflowOperationGroupId(WorkflowOperationGroup.ReferenceId);

        Logger.Information($"{actionName}: DRCustomAction execution - Retrieving action count");

        var completedActions = workflowActionResults
            .Where(x => new[] { "success", "skip" }.Contains(x.Status.ToLower().Trim()))
            .ToList();

        Logger.Information($"DRCustomAction execution - Completed action count: {completedActions.Count}");
        string progressStatus = $"{completedActions.Count}/{totalActionCount}";
        string status = "Success";
        if (completedActions.Count == totalActionCount)
        {
            Logger.Debug($"{actionName}: Total success count ({completedActions.Count}) equals total action count ({totalActionCount})");
            status = "Completed";
        }
        await _actionHelper.UpdateWorkflowOperationGroupInfra(status, progressStatus, actionName, actionId, "");
        Logger.Debug($"{actionName}: Updated Action Status: {status} | Progress Status: {progressStatus}");
        return "Success";
    }

    private async Task DependentAction(dynamic workflowActionProperties)
    {
        var workflowId = string.Empty;

        var stepId = string.Empty;

        foreach (var actionProperty in workflowActionProperties)
        {
            string name = actionProperty.Name.ToString().ToLower();
            string value = actionProperty.Value.ToString();

            if (name.Contains("workflow_name") || name.Contains("dependentworkflowname"))
            {
                workflowId = value;
            }

            if (name.Contains("workflow_action") || name.Contains("dependentworkflowaction"))
            {
                stepId = value;
            }
        }
        if (!string.IsNullOrEmpty(workflowId) && !string.IsNullOrEmpty(stepId))
        {
            await _actionHelper.WaitForWorkflowActionCompleted(workflowId, stepId);
        }
        else
        {
            Logger.Warning("Missing workflow or action information. WorkflowId or StepId is empty.");
        }
    }

    public async Task<string> RunActionScript(string script)
    {
        Logger.Debug("Entered into RunActionScript method");

        var result = await ProcessScript(script);

        Logger.Debug("RunActionScript method ended.");

        return result;
    }

    private async Task<string> ProcessScript(string script)
    {
        Logger.Debug("Entered into ProcessScript method");

        try
        {
            var result = await InterpreterInstance.ProcessAsync(script, "", true);

            Logger.Debug("ProcessScript method ended");

            return result.ToString();
        }
        catch (Exception exc)
        {
            Logger.Exception("Exception occurred while processing script.", exc);

            InterpreterInstance.InvalidateStacksAfterLevel(0);
            throw;
        }
    }
    private async Task<string> UpdateWorkflowScheduleStatus(string ScheduledId, string status)
    {
        string resultStatus;
        try
        {
            status = status.Equals("disable", StringComparison.OrdinalIgnoreCase) ? "Close" : "Open";

            var scheduledWorkflowActionList = await DataProvider.SchedulerWorkflowActionsResult.GetByReferenceIdAsync(ScheduledId);



            if (scheduledWorkflowActionList != null)
            {

                scheduledWorkflowActionList.Status = status;
                await DataProvider.SchedulerWorkflowActionsResult.UpdateAsync(scheduledWorkflowActionList);

                Logger.Information("UpdateWorkflowScheduleStatus status " + status);
                resultStatus = "Success";
            }
            else
            {
                Logger.Information("UpdateWorkflowScheduleStatus throws Empty Data");
                resultStatus = "Error";
            }
        }
        catch (Exception exc)
        {
            resultStatus = "Error";
            Logger.Error("UpdateWorkflowScheduleStatus throws error." + exc.Message);
        }
        return resultStatus;
    }
    private async Task<string> UpdateSnapStatus(string snapId, string status)
    {
        string resultStatus;
        try
        {
            var snapList = await DataProvider.CyberSnap.GetCyberSnapById(snapId);
            if (snapList != null)
            {
                snapList.Tag = status;
                await DataProvider.CyberSnap.UpdatecyberSnapAsync(snapList);

                Logger.Information("UpdateCyberAirGapStatus status Success");
                resultStatus = "Success";
            }
            else
            {
                resultStatus = "Error";
            }

        }
        catch (Exception)
        {

            resultStatus = "Error";
        }
        return resultStatus;
    }
    private async Task<string> UpdateCyberAirGapStatus(string airGapId, string status)
    {
        string resultStatus;
        try
        {
            status = status.Equals("disable", StringComparison.OrdinalIgnoreCase) ? "Close" : "Open";

            var airGapList = await DataProvider.CyberAirGap.GetCyberAirGapById(airGapId);

            CheckPropertyIsNullOrEmpty(airGapList, "airGapList is null in ", "UpdateCyberAirGapStatus");

            if (airGapList != null)
            {
                var rpo = DateTime.Now - airGapList.StartTime;

                airGapList.RPO = rpo.TotalSeconds > 59
                                 ? $"{(int)rpo.TotalMinutes} Min"
                                 : $"{(int)rpo.TotalSeconds} Sec";

                airGapList.Status = status;
                airGapList.EndTime = DateTime.Now;
                airGapList.WorkflowStatus = "Completed";
                await DataProvider.CyberAirGap.UpdateSignalRAirGapStatus(airGapList);

                Logger.Information("UpdateCyberAirGapStatus status Success");
                resultStatus = "Success";
            }
            else
            {
                Logger.Information("AirGapList throws Empty Data");
                resultStatus = "Error";
            }
        }
        catch (Exception exc)
        {
            resultStatus = "Error";
            var airGapList = await DataProvider.CyberAirGap.GetCyberAirGapById(airGapId);
            if (airGapList != null)
            {
                airGapList.WorkflowStatus = resultStatus;
                airGapList.ErrorMessage = exc.Message;
                await DataProvider.CyberAirGap.UpdatecyberAirGapAsync(airGapList);
            }

            Logger.Exception("UpdateCyberAirGapStatus throws error.", exc);
        }
        return resultStatus;
    }

    void CheckPropertyIsNullOrEmpty(dynamic propertyValue, string errorMessage, string methodName)
    {
        if (string.IsNullOrEmpty(propertyValue?.ToString()))
        {
            string message = errorMessage + "" + "MethodName :" + methodName + "," + "ClassName :" + "SequentialActionExecutor.";
            Logger.Error(message);
            throw new Exception(message);
        }
    }


    private async Task<string> MonitoringAirGapAsync(string status, CyberAirGap airGapList)
    {
        if (status.Equals("Lock", StringComparison.OrdinalIgnoreCase))
        {
            airGapList.EndTime = DateTime.Now;
            var rpo = airGapList.EndTime - airGapList.StartTime;
            airGapList.RPO = rpo.TotalSeconds > 59
             ? $"{(int)rpo.TotalMinutes} Min"
             : $"{(int)rpo.TotalSeconds} Sec";
        }
        else
        {
            airGapList.StartTime = DateTime.Now;
            airGapList.EndTime = DateTime.Parse("0001-01-01 00:00:00.0000000");
            airGapList.RPO = "NA";
        }

        airGapList.Status = status;
        airGapList.WorkflowStatus = "Completed";
        await DataProvider.CyberAirGap.UpdatecyberAirGapAsync(airGapList);
        await UpdateCyberAirGapLogsAndStatus(airGapList);
        Logger.Information("UpdateCyberAirGapStatus status Success");
        return "Success";
    }


    public async Task CyberAirGapAlerts(ContinuityPatrolException exception, CyberAirGap cyberAirGap)
    {
        try
        {
            Logger.Debug($"Entered into CyberAirGapAlerts method");
            var alertId = ((int)exception.ExceptionType).ToString();

            var alertMaster = await DataProvider.AlertMaster.GetAlertByAlertId(alertId);
            if (alertMaster != null)
            {
                var tmpData = alertMaster.AlertMessage;
                tmpData = tmpData.Replace("{200}", cyberAirGap.Name).Replace("{203}", cyberAirGap?.Port.ToString());

                var sourceComponent = await DataProvider.CyberComponentService.GetCyberComponentById(cyberAirGap.SourceComponentId);
                if (sourceComponent != null)
                {
                    tmpData = tmpData.Replace("{201}", sourceComponent?.Name ?? string.Empty);
                }

                var targetComponent = await DataProvider.CyberComponentService.GetCyberComponentById(cyberAirGap.TargetComponentId);
                if (targetComponent != null)
                {
                    tmpData = tmpData.Replace("{202}", targetComponent?.Name ?? string.Empty);
                }
                tmpData = tmpData.Replace("{201}", cyberAirGap?.SourceComponentName ?? string.Empty).Replace("{202}", cyberAirGap?.TargetComponentName ?? string.Empty);
                alertMaster.AlertMessage = tmpData;

                Logger.Information($"Alert name for send alert is '{alertMaster.AlertName}'");
                await WriteDatabase(alertMaster, DataProvider, Logger, "AirGapJob", exception, "AirGap", cyberAirGap.ReferenceId);
                await SendMailAsync(cyberAirGap.Status, cyberAirGap);
            }
            else
            {
                Logger.Information($"Alert master value not available for this alert id is'{alertId}'");
            }

            Logger.Debug("RaiseNegativeAlert method ended");
        }
        catch (Exception exc)
        {
            Logger.Information("RaiseNegativeAlert method Exception :" + exc.ToString());
        }
    }
    public async Task<List<Email>> GetAlertReceiverEmailIdsByInfraObjectId()
    {
        var alertReceivers = new List<Email>();
        try
        {
            var message = await DataProvider.AlertReceiver.GetAlertReceiver();
            message.ForEach(alertReceiver =>
            {
                if (!alertReceiver.IsActiveUser) return;
                var email = new Email
                {
                    ToMail = alertReceiver.EmailAddress,
                    Name = alertReceiver.Name
                };
                alertReceivers.Add(email);
            });
            return alertReceivers;
        }
        catch (Exception ex)
        {
            Logger.Error($"Error occurring while Get receiver mails in GetAlertReceiverEmailIdsByInfraObjectId method. error is : {ex.Message}");
            return alertReceivers;
        }
    }

    public async Task<bool> SendMailAsync(string actionResultStatus, CyberAirGap airGap)
    {
        try
        {
            Logger.Information($"Send mail Workflow Action Alert notification method started.");

            var mails = await GetAlertReceiverEmailIdsByInfraObjectId();

            string type = string.Empty;
            string errorMessage = string.Empty;
            if (actionResultStatus.Equals("Open", StringComparison.OrdinalIgnoreCase))
            {
                errorMessage = $@"</div>
                            <h2>CPAlert : Cyber AirGap Opened </h2>
                            <div class='col-8'>
                                <p><strong>AirGap Name:</strong> {airGap.Name}</p>                                         
                                 <p><strong>Source :</strong> {airGap.SourceComponentName}</p>    
                                <p><strong>Target :</strong> {airGap.TargetComponentName}</p>   
                                <p><strong>Start Time :</strong> {airGap.StartTime}</p>                              
                                <p><strong>Status :</strong> {actionResultStatus}</p>                                                                 
                            </div>";
                type = "airgapopen";
            }
            else if (actionResultStatus.Equals("Close", StringComparison.OrdinalIgnoreCase))
            {
                errorMessage = $@"</div>
                            <h2>CPAlert : Cyber AirGap Closed </h2>
                            <div class='col-8'>
                                <p><strong>AirGap Name:</strong> {airGap.Name}</p>                                         
                                 <p><strong>Source :</strong> {airGap.SourceComponentName}</p>    
                                <p><strong>Target :</strong> {airGap.TargetComponentName}</p>   
                                <p><strong>End Time :</strong> {airGap.EndTime}</p>                              
                                <p><strong>Status :</strong> {actionResultStatus}</p>                
                            </div>";

                type = "airgapclose";
            }
            var alert = new SendEmailCommand
            {
                Type = type,
                ErrorMessage = errorMessage,
                Emails = mails
            };

            Mail mail = new(Logger, DataProvider);
            await mail.SendMailToUsers(alert);

            return true;
        }
        catch (Exception exc)
        {
            Logger.Error($"Send mail Alert notification Success Scenario- 'false'. {exc.Message}");

            return false;
        }
    }

    public async Task WriteDatabase(AlertMaster alertMasterInfo, IDataProvider dataProvider, ILogger logger, string entityName, ContinuityPatrolException exception, string entity, string entityId)
    {
        try
        {
            await dataProvider.CyberAlertService.CreateCyberAlert(new CyberAlert
            {
                Type = alertMasterInfo.AlertName,
                Severity = alertMasterInfo.AlertPriority,
                SystemMessage = alertMasterInfo.AlertMessage,
                UserMessage = exception.UserDefinedMessage,
                JobName = entityName,
                ClientAlertId = alertMasterInfo.AlertId,
                IsResolve = 0,
                IsAcknowledgement = 0,
                EntityId = entityId,
                EntityType = entity,
                AlertCategoryId = 0
            });
        }
        catch (Exception ex)
        {
            logger.Error($"Exception occured in WriteDatabase method. Exception :{ex.Message}");
        }
    }

    public async Task UpdateCyberAirGapLogsAndStatus(CyberAirGap cyberAirGap)
    {
        try
        {
            var airGapStatus = await DataProvider.CyberAirGapStatusService.GetCyberAirGapStatusByAirGapId(cyberAirGap.ReferenceId) ?? new CyberAirGapStatus();

            await MapCyberAirGapToStatus(cyberAirGap, airGapStatus);

            if (airGapStatus.Id == 0)
            {
                await DataProvider.CyberAirGapStatusService.CreateCyberAirGapStatus(airGapStatus);
                Logger.Information("Cyber airgap status created");
            }
            else
            {
                await DataProvider.CyberAirGapStatusService.UpdateCyberAirGapStatus(airGapStatus);
                Logger.Information("Cyber airgap status updated");
            }

            var airGapLog = new CyberAirGapLog();
            await MapCyberAirGapToLog(cyberAirGap, airGapLog);
            await DataProvider.CyberAirGapLogService.CreateCyberAirGapLog(airGapLog);
            Logger.Information("Cyber airgap log created");
        }
        catch (Exception ex)
        {
            Logger.Exception("Exception occurred in UpdateCyberAirGapLogsAndStatus method", ex);
        }
    }
    private static Task MapCyberAirGapToStatus(CyberAirGap cyberAirGap, CyberAirGapStatus status)
    {
        status.AirGapId = cyberAirGap.ReferenceId;
        status.AirGapName = cyberAirGap.Name;
        status.Source = cyberAirGap.Source;
        status.SourceComponentId = cyberAirGap.SourceComponentId;
        status.SourceComponentName = cyberAirGap.SourceComponentName;
        status.Target = cyberAirGap.Target;
        status.TargetComponentId = cyberAirGap.TargetComponentId;
        status.TargetComponentName = cyberAirGap.TargetComponentName;
        status.RPO = cyberAirGap.RPO;
        status.Status = cyberAirGap.Status;
        status.StartTime = cyberAirGap.StartTime;
        status.EndTime = cyberAirGap.EndTime;
        status.Description = cyberAirGap.Description;
        status.EnableWorkflowId = cyberAirGap.EnableWorkflowId;
        status.DisableWorkflowId = cyberAirGap.DisableWorkflowId;
        status.WorkflowStatus = cyberAirGap.WorkflowStatus;
        status.SourceSiteId = cyberAirGap.SourceSiteId;
        status.SourceSiteName = cyberAirGap.SourceSiteName;
        status.TargetSiteId = cyberAirGap.TargetSiteId;
        status.TargetSiteName = cyberAirGap.TargetSiteName;
        status.Port = cyberAirGap.Port;
        return Task.CompletedTask;
    }

    private static Task MapCyberAirGapToLog(CyberAirGap cyberAirGap, CyberAirGapLog log)
    {
        log.AirGapId = cyberAirGap.ReferenceId;
        log.AirGapName = cyberAirGap.Name;
        log.Source = cyberAirGap.Source;
        log.SourceComponentId = cyberAirGap.SourceComponentId;
        log.SourceComponentName = cyberAirGap.SourceComponentName;
        log.Target = cyberAirGap.Target;
        log.TargetComponentId = cyberAirGap.TargetComponentId;
        log.TargetComponentName = cyberAirGap.TargetComponentName;
        log.RPO = cyberAirGap.RPO;
        log.Status = cyberAirGap.Status;
        log.StartTime = cyberAirGap.StartTime;
        log.EndTime = cyberAirGap.EndTime;
        log.Description = cyberAirGap.Description;
        log.EnableWorkflowId = cyberAirGap.EnableWorkflowId;
        log.DisableWorkflowId = cyberAirGap.DisableWorkflowId;
        log.WorkflowStatus = cyberAirGap.WorkflowStatus;
        log.SourceSiteId = cyberAirGap.SourceSiteId;
        log.SourceSiteName = cyberAirGap.SourceSiteName;
        log.TargetSiteId = cyberAirGap.TargetSiteId;
        log.TargetSiteName = cyberAirGap.TargetSiteName;
        log.Port = cyberAirGap.Port;
        return Task.CompletedTask;
    }
    async Task<string> CheckNullOrEmpty_BeforeWorkflow_Initialize(dynamic propertyValue, string errorMessage, String methodname, string _actionName, string wfActionId, string stepId)
    {
        if (string.IsNullOrEmpty(propertyValue?.ToString()))
        {
            string message = errorMessage + "" + "MethodName :" + methodname + "," + "ClassName :" + "SequentialActionExecutor.";
            Logger.Error(message);
            _workflowActionReturnResult = await _actionHelper.InsertWorkflowActionResult(_actionName, wfActionId, stepId, "Error", false);
            throw new Exception(message);

        }
        return "";
    }
}