﻿using Serilog;
using System.Collections.Concurrent;

namespace ContinuityPatrol.Language.Parser
{
    public class ParsingScript
    {

        public ILogger? Logger { get; set; }

        private string? _filename;
        private string _functionName = "";// filename containing the script

        public int Pointer { get; set; }
        public string String { get; set; }

        public string Rest => Substr(Pointer, Constants.MAX_CHARS_TO_SHOW);
        public char Current => Pointer < String.Length ? String[Pointer] : Constants.EMPTY;

        public char Prev => Pointer >= 1 ? String[Pointer - 1] : Constants.EMPTY;
        public char PrevPrev => Pointer >= 2 ? String[Pointer - 2] : Constants.EMPTY;

        public char Next => Pointer + 1 < String.Length ? String[Pointer + 1] : Constants.EMPTY;

        public ConcurrentDictionary<int, int> Char2Line { get; set; } = null!;

        public int ScriptOffset { get; set; } = 0;

        public string Filename
        {
            get => _filename!;
            set => _filename = Utils.GetFullPath(value);
        }
        public string Pwd => Utils.GetDirectoryName(_filename!);

        public string? OriginalScript { get; set; }

        public string? CurrentAssign { get; set; }

        public Debugger? Debugger
        {
            get;
            set;
        }

        public string? CurrentModule { get; set; }

        public ConcurrentDictionary<string, ConcurrentDictionary<string, int>> AllLabels
        {
            get;
            set;
        } = null!;
        public ConcurrentDictionary<string, string> LabelToFile
        {
            get;
            set;
        } = null!;

        public List<int> PointersBack { get; set; } = new List<int>();

        public object? Context { get; set; }


        public string FunctionName
        {
            get => _functionName;
            set => _functionName = value.ToLower();
        }

        public ParserFunction.StackLevel? StackLevel { get; set; }
        public bool ProcessingList { get; set; }

        public bool DisableBreakpoints;
        public bool InTryBlock;
        public string? MainFilename;

        public int ParentOffset { get; set; }

        public ParsingScript? ParentScript;

        public ParserEngine? CurrentClass { get; set; }
        public ParserEngine.ClassInstance? ClassInstance { get; set; }

        public Interpreter InterpreterInstance { get; private set; }

        public ParsingScript(Interpreter interpreter, string data, int from = 0, ConcurrentDictionary<int, int> char2Line = null!)
        {
            InterpreterInstance = interpreter;
            Logger = interpreter.Logger;
            Logger?.Debug("ParsingScript Constructor InterpreterInstance : " + interpreter.GetHashCode());
            String = data;
            Pointer = from;
            Char2Line = char2Line;
        }

        public ParsingScript(ParsingScript other)
        {
            InterpreterInstance = other.InterpreterInstance;
            Logger = other.InterpreterInstance.Logger;
            Logger?.Debug("ParsingScript Other Constructor InterpreterInstance : " + other.InterpreterInstance.GetHashCode());
            String = other.String;
            Pointer = other.Pointer;
            Char2Line = other.Char2Line;
            _filename = other.Filename;
            OriginalScript = other.OriginalScript;
            StackLevel = other.StackLevel;
            CurrentClass = other.CurrentClass;
            ClassInstance = other.ClassInstance;
            ScriptOffset = other.ScriptOffset;
            Debugger = other.Debugger;
            InTryBlock = other.InTryBlock;
            AllLabels = other.AllLabels;
            LabelToFile = other.LabelToFile;
            FunctionName = other.FunctionName;
        }

        // Use a method instead of a settable property, because
        // this should be very rare, and we want to flag it.
        public void SetInterpreter(Interpreter interpreter)
        {
            Logger?.Debug("Parsing Script SetInterpreter Interpreter HashCode :" + interpreter.GetHashCode());

            InterpreterInstance = interpreter;
        }

        public int Size() { return String.Length; }
        public bool StillValid() { return Pointer < String.Length; }

        public void SetDone() { Pointer = String.Length; }

        public string GetFilePath(string path)
        {
            if (!Path.IsPathRooted(path))
            {
                string pathname = Path.Combine(Pwd, path);
                if (File.Exists(pathname))
                {
                    return pathname;
                }
                pathname = Path.GetFullPath(path);
                if (File.Exists(pathname))
                {
                    return pathname;
                }
            }
            return path;
        }

        public bool StartsWith(string str, bool caseSensitive = true)
        {
            if (String.IsNullOrEmpty(str) || str.Length > String.Length - Pointer)
            {
                return false;
            }
            for (int i = Pointer; i < String.Length && i < str.Length + Pointer; i++)
            {
                var ch1 = str[i - Pointer];
                var ch2 = String[i];

                if ((caseSensitive && ch1 != ch2) ||
                   (!caseSensitive && char.ToUpperInvariant(ch1) != char.ToUpperInvariant(ch2)))
                {
                    return false;
                }
            }

            return true;
        }

        public bool ProcessReturn()
        {
            if (PointersBack.Count > 0)
            {
                Pointer = PointersBack[PointersBack.Count - 1];
                PointersBack.RemoveAt(PointersBack.Count - 1);
                return true;
            }
            return false;
        }

        public int Find(char ch, int from = -1)
        { return String.IndexOf(ch, from < 0 ? Pointer : from); }

        public int FindFirstOf(string str, int from = -1)
        { return FindFirstOf(str.ToCharArray(), from); }

        public int FindFirstOf(char[] arr, int from = -1)
        { return String.IndexOfAny(arr, from < 0 ? Pointer : from); }

        public string Substr(int fr = -2, int len = -1)
        {
            int from = Math.Min(Pointer, String.Length - 1);
            fr = fr == -2 ? from : fr == -1 ? 0 : fr;
            return len < 0 || len >= String.Length - fr ? String[fr..] : String.Substring(fr, len);
        }

        public string GetStack(int firstOffset = 0)
        {
            ThreadSafeStringBuilder result = new ThreadSafeStringBuilder();
            ParsingScript script = this;

            while (script != null)
            {
                int pointer = script == this ? script.Pointer + firstOffset : script.Pointer;
                int lineNumber = script.GetOriginalLineNumber(pointer);
                string filename = string.IsNullOrWhiteSpace(script.Filename) ? "" :
                                  Utils.GetFullPath(script.Filename);
                string line = string.IsNullOrWhiteSpace(filename) || !File.Exists(filename) ? "" :
                              File.ReadLines(filename).Skip(lineNumber).Take(1).First();

                result.AppendLine("" + lineNumber);
                result.AppendLine(filename);
                result.AppendLine(line.Trim());

                script = script.ParentScript!;
            }

            return result.ToString().Trim();
        }

        public string GetOriginalLine(out int lineNumber)
        {
            lineNumber = GetOriginalLineNumber();
            if (lineNumber < 0 || OriginalScript == null)
            {
                return "";
            }

            string[] lines = OriginalScript.Split(Constants.END_LINE);
            if (lineNumber < lines.Length)
            {
                return lines[lineNumber];
            }

            return "";
        }

        public int OriginalLineNumber => GetOriginalLineNumber();

        public string OriginalLine
        {
            get
            {
                int lineNumber;
                return GetOriginalLine(out lineNumber);
            }
        }

        public int GetOriginalLineNumber()
        {
            return GetOriginalLineNumber(Pointer);
        }
        public int GetOriginalLineNumber(int charNumber)
        {
            if (Char2Line == null || Char2Line.Count == 0)
            {
                return -1;
            }

            int pos = ScriptOffset + charNumber;
            List<int> lineStart = Char2Line.Keys.ToList();
            int lower = 0;
            int index = lower;

            if (pos <= lineStart[lower])
            { // First line.
                return Char2Line[lineStart[lower]];
            }
            int upper = lineStart.Count - 1;
            if (pos >= lineStart[upper])
            { // Last line.
                return Char2Line[lineStart[upper]];
            }

            while (lower <= upper)
            {
                index = (lower + upper) / 2;
                int guessPos = lineStart[index];
                if (pos == guessPos)
                {
                    break;
                }
                if (pos < guessPos)
                {
                    if (index == 0 || pos > lineStart[index - 1])
                    {
                        break;
                    }
                    upper = index - 1;
                }
                else
                {
                    lower = index + 1;
                }
            }

            int charIndex = lineStart[index];
            return Char2Line[charIndex];
        }

        public char At(int i) { return String[i]; }
        public char CurrentAndForward() { return String[Pointer++]; }

        public char TryCurrent()
        {
            return Pointer < String.Length ? String[Pointer] : Constants.EMPTY;
        }
        public char TryNext()
        {
            return Pointer + 1 < String.Length ? String[Pointer + 1] : Constants.EMPTY;
        }
        public char TryPrev()
        {
            return Pointer >= 1 ? String[Pointer - 1] : Constants.EMPTY;
        }
        public char TryPrevPrev()
        {
            return Pointer >= 2 ? String[Pointer - 2] : Constants.EMPTY;
        }
        public char TryPrevPrevPrev()
        {
            return Pointer >= 3 ? String[Pointer - 3] : Constants.EMPTY;
        }

        public string FromPrev(int backChars = 1, int maxChars = Constants.MAX_CHARS_TO_SHOW)
        {
            int from = Math.Max(0, Pointer - backChars);
            int max = Math.Min(String.Length - from, maxChars);
            string result = String.Substring(from, max);
            return result;
        }

        public bool IsPrevious(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return true;
            }
            if (Pointer < str.Length || String.Length < str.Length)
            {
                return false;
            }

            var substr = String.Substring(Pointer - str.Length, str.Length);
            return substr.Equals(str, StringComparison.OrdinalIgnoreCase);
        }

        public void Forward(int delta = 1) { Pointer += delta; }
        public void Backward(int delta = 1) { if (Pointer >= delta) Pointer -= delta; }

        public void MoveForwardIf(char[] arr)
        {
            foreach (char ch in arr)
            {
                if (MoveForwardIf(ch))
                {
                    return;
                }
            }
        }
        public bool MoveForwardIf(char expected, char expected2 = Constants.EMPTY)
        {
            if (StillValid() && (Current == expected || Current == expected2))
            {
                Forward();
                return true;
            }
            return false;
        }
        public void MoveBackIf(char notExpected)
        {
            if (StillValid() && Pointer > 0 && Current == notExpected)
            {
                Backward();
            }
        }
        public void MoveBackIfPrevious(char ch)
        {
            if (Prev == ch)
            {
                Backward();
            }
        }
        public void MoveForwardIfNotPrevious(char ch)
        {
            if (Prev != ch)
            {
                Forward();
            }
        }
        public void SkipAllIfNotIn(char toSkip, char[] to)
        {
            if (to.Contains(toSkip))
            {
                return;
            }
            while (StillValid() && Current == toSkip)
            {
                Forward();
            }
        }

        public List<Variable> GetFunctionArgs(char start = Constants.START_ARG, char end = Constants.END_ARG)
        {
            bool isList;
            List<Variable> args = Utils.GetArgs(this, start, end, (outList) => { isList = outList; });
            return args;
        }
        public async Task<List<Variable>> GetFunctionArgsAsync(char start = Constants.START_ARG, char end = Constants.END_ARG)
        {
            bool isList;

            List<Variable> args = await Utils.GetArgsAsync(this,
                                                start, end, (outList) => { isList = outList; });
            return args;
        }

        public bool IsProcessingFunctionCall()
        {
            if (TryPrev() == Constants.START_ARG || TryCurrent() == Constants.START_ARG)
            {
                return true;
            }
            return false;
        }

        public int GoToNextStatement()
        {
            int endGroupRead = 0;

            while (StillValid())
            {
                char currentChar = Current;
                switch (currentChar)
                {
                    case Constants.END_GROUP:
                        endGroupRead++;
                        Forward();                  // '}'
                        return endGroupRead;
                    case Constants.START_GROUP:     // '{'
                    case Constants.QUOTE:           // '"'
                    case Constants.SPACE:           // ' '
                    case Constants.END_STATEMENT:   // ';'
                    case Constants.END_ARG:         // ')'
                        Forward();
                        break;
                    default: return endGroupRead;
                }
            }
            return endGroupRead;
        }

        public static Variable RunString(Interpreter interpreter, string str)
        {
            ParsingScript tempScript = new ParsingScript(interpreter, str);
            Variable result = tempScript.Execute();
            return result;
        }

        public Variable Execute(char[] toArray = null!, int from = -1)
        {
            toArray = toArray == null ? Constants.END_PARSE_ARRAY : toArray;
            Pointer = from < 0 ? Pointer : from;

            if (!String.EndsWith(Constants.END_STATEMENT.ToString()))
            {
                String += Constants.END_STATEMENT;
            }

            Variable result = null!;

            bool handleByDebugger = Debugger != null && DebuggerServer.DebuggerAttached && !Debugger.Executing;
            if (handleByDebugger)
            {
                result = Debugger.CheckBreakpoints(this).Result;
                if (result != null)
                {
                    return result;
                }
            }

            if (InTryBlock)
            {
                result = new Parser().SplitAndMerge(this, toArray);
            }
            else
            {
                try
                {
                    result = new Parser().SplitAndMerge(this, toArray);
                }
                catch (ParsingException parseExc)
                {
                    if (handleByDebugger)
                    {
                        Debugger.ProcessException(this, parseExc);
                    }
                    throw;
                }
                catch (Exception exc)
                {
                    ParsingException parseExc = new ParsingException(exc.Message, this, exc);
                    if (handleByDebugger)
                    {
                        Debugger.ProcessException(this, parseExc);
                    }
                    throw parseExc;
                }
            }
            return result;
        }

        public async Task<Variable> ExecuteAsync(char[] toArray = null!, int from = -1)
        {
            toArray = toArray == null ? Constants.END_PARSE_ARRAY : toArray;

            Pointer = from < 0 ? Pointer : from;

            if (!String.EndsWith(Constants.END_STATEMENT.ToString()))
            {
                String += Constants.END_STATEMENT;
            }

            Variable result = null!;                    

            if (InTryBlock)
            {
                result = await new Parser().SplitAndMergeAsync(this, toArray);

                Logger?.Debug("ExecuteAsync: SplitAndMergeAsync - InTryBlock Result {0}", result);
            }
            else
            {
                try
                {
                    result = await new Parser().SplitAndMergeAsync(this, toArray);

                    //Logger?.Debug("ExecuteAsync: SplitAndMergeAsync - Result {0}", result);
                }
                catch (ParsingException parseExc)
                {
                    Logger?.Error($"ExecuteAsync: ParsingException {parseExc.Message}");

                    Logger?.Debug($"ExecuteAsync: ParsingException Trace {parseExc.StackTrace}");
                    throw;
                }
                catch (Exception exc)
                {
                    Logger?.Error($"ExecuteAsync: Exception {exc.Message}");

                    Logger?.Debug($"ExecuteAsync: Exception Trace {exc.StackTrace}");

                    ParsingException parseExc = new ParsingException(exc.Message, this, exc);

                    throw parseExc;
                }
            }
            return result;
        }

        public Variable ExecuteAll()
        {
            Variable result = null!;
            while (StillValid())
            {
                result = Execute(Constants.END_LINE_ARRAY);
                GoToNextStatement();
            }
            return result;
        }
        public Variable ExecuteScript()
        {
            Variable result = null!;
            while (StillValid() &&
                  (result == null || !result.IsReturn))
            {
                result = Execute();
                GoToNextStatement();
            }
            return result;
        }

        public ParsingScript GetTempScript(string str, int startIndex = 0)
        {
            ParsingScript tempScript = new ParsingScript(InterpreterInstance, str, startIndex);
            tempScript.Filename = this.Filename;
            tempScript.InTryBlock = this.InTryBlock;
            tempScript.ParentScript = this;
            tempScript.Char2Line = this.Char2Line;
            tempScript.OriginalScript = this.OriginalScript;
            tempScript.InTryBlock = this.InTryBlock;
            tempScript.StackLevel = this.StackLevel;
            tempScript.AllLabels = this.AllLabels;
            tempScript.LabelToFile = this.LabelToFile;
            tempScript.FunctionName = this.FunctionName;

            //tempScript.Debugger       = this.Debugger;

            return tempScript;
        }

        public ParsingScript GetIncludeFileScript(string filename)
        {
            string pathname = GetFilePath(filename);
            string[] lines = Utils.GetFileLines(pathname);

            string includeFile = string.Join(Environment.NewLine, lines);
            ConcurrentDictionary<int, int> char2Line;
            var includeScript = Utils.ConvertToScript(InterpreterInstance, includeFile, out char2Line, pathname);
            ParsingScript tempScript = new ParsingScript(InterpreterInstance, includeScript, 0, char2Line);
            tempScript.Filename = pathname;
            tempScript.OriginalScript = string.Join(Constants.END_LINE.ToString(), lines);
            tempScript.ParentScript = this;
            tempScript.InTryBlock = InTryBlock;

            return tempScript;
        }
    }

    public class ParsingException : Exception
    {
        public ParsingScript? ExceptionScript { get; private set; }
        public string ExceptionStack { get; private set; } = "";

        public ParsingException(string message, string excStack = "")
            : base(message)
        {
            ExceptionStack = excStack.Trim();
        }
        public ParsingException(string message, ParsingScript script)
            : base(message)
        {
            ExceptionScript = script;
            ExceptionStack = script.GetStack(-1);
        }
        public ParsingException(string message, ParsingScript script, Exception inner)
            : base(message, inner)
        {
            ExceptionScript = script;
            ExceptionStack = script.GetStack(-1);
        }
    }
}
