﻿using ContinuityPatrol.CrossCuttingConcerns.Extensions;
using ContinuityPatrol.CrossCuttingConcerns.Shared;
using ContinuityPatrol.Data.Shared.Contract;
using ContinuityPatrol.Data.Shared.Models;
using ContinuityPatrol.ExceptionHandler;
using ContinuityPatrol.Language.Parser.InterpreterManager;
using DevExpress.Charts.Native;
using DevExpress.CodeParser;
using DevExpress.DataAccess;

namespace ContinuityPatrol.Workflow.Engine
{
    public class WorkflowHelper
    {
        private readonly WorkflowOperationGroup _operationGroup;
        private readonly IDataProvider _dataProvider;
        private readonly ILogger _logger;
        private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(500, 1000);

        public WorkflowHelper(WorkflowOperationGroup operationGroup, IDataProvider dataProvider, ILogger logger)
        {
            _operationGroup = operationGroup;
            _dataProvider = dataProvider;
            _logger = logger;
        }


        public int GetIndexValue(ServiceType type, WorkflowOperationGroup operationGroup)
        {
            if (type == ServiceType.WORKFLOW_SERVICE && (operationGroup.IsResume == 1 || operationGroup.ConditionalOperation == (int)ConditionalOperation.TERMINATE))
            {

                return Convert.ToInt32(operationGroup.ProgressStatus.Split("/")[0]);
              
            }

            return 0;
        }



        public async Task<int> GeParallellIndexValueNew(ServiceType type, WorkflowOperationGroup operationGroup, dynamic dynJsonWorkflow)
        {
            _logger.Information("Checking GeParallellIndexValue for termination/resume conditions...");

            bool shouldCheckTermination = type == ServiceType.WORKFLOW_SERVICE &&
                                       (operationGroup.IsResume == 1 ||
                                        operationGroup.ConditionalOperation == (int)ConditionalOperation.TERMINATE);

            if (!shouldCheckTermination)
            {
                _logger.Information("No termination/resume conditions met, returning default index 0");
                return 0;
            }

            _logger.Information("Termination/resume condition detected, processing failed actions...");

            try
            {
                var failedActions = await _dataProvider.WorkflowActionResult
                    .GetWorkflowActionResultByWorkflowOperationGroupId(operationGroup.ReferenceId);

                var errorActions = failedActions?
                    .Where(x => x.Status == "Error")
                    .ToList();

                if (errorActions == null || !errorActions.Any())
                {
                    _logger.Information("No failed actions found, returning default index 0");
                    return 0;
                }

                _logger.Information($"Found {errorActions.Count} failed actions to process");

                var nodes = dynJsonWorkflow?.SelectToken("nodes");
                if (nodes == null)
                {
                    _logger.Information("No nodes found in workflow JSON");
                    return 0;
                }
               
                for (int i = 0; i < dynJsonWorkflow?.SelectToken("nodes").Count; i++)
                {
                    var children = nodes[i]?.SelectToken("children");
                    if (children == null) continue;

                    _logger.Information($"Checking children of parent node {i}");

                    for (int j = 0; j < children.Count; j++)
                    {
                        var childNode = children[j];
                        var stepId = childNode?.SelectToken("stepId")?.Value<string>();

                        if (string.IsNullOrEmpty(stepId)) continue;

                        // Check if this step matches any failed action
                        if (errorActions.Any(failed => failed.StepId == stepId))
                        {
                            _logger.Information($"Terminate condition matched at parent index: {i} (child stepId: {stepId})");
                            return i; // Return the parent index where failed child exists
                        }
                    }
                }

                _logger.Information("No matching failed actions found in workflow nodes");
                return 0;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error in termination index calculation: {ex.Message}");
                return 0; // Fail-safe return
            }

        }
        public async Task<int> GeParallellIndexValue(ServiceType type, WorkflowOperationGroup operationGroup, dynamic dynJsonWorkflow)
        {
            _logger.Information("Terminate GeParallellIndexValue: ");
            bool isCheck = false;
            if (type == ServiceType.WORKFLOW_SERVICE && (operationGroup.IsResume == 1 || operationGroup.ConditionalOperation == (int)ConditionalOperation.TERMINATE))
            {

                _logger.Information("Terminate GeParallellIndexValue True: ");
                var WorklowActionResultList =
                                await _dataProvider.WorkflowActionResult
                                    .GetWorkflowActionResultByWorkflowOperationGroupId(operationGroup
                                        .ReferenceId);
                var actionResultList = WorklowActionResultList
                                        .Where(x => x.Status == "Error").ToList();
                foreach (var item in actionResultList)
                {
                    var resultStepId = item.StepId;
                    for (int i = 0; i < dynJsonWorkflow!.SelectToken("nodes").Count; i++)
                    {
                        var checkchild = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children");

                        _logger.Information("RunParallelWorkflow-checkchild: " + checkchild);


                        for (int j = 0; j < dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children").Count; j++)
                        {

                            var getChildNode = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children")[j];
                            var test = getChildNode.SelectToken("actionInfo");
                            var stepId = getChildNode.SelectToken("stepId").Value;
                            if (resultStepId == stepId)
                            {
                                _logger.Information("Terminate Index : " +i); 
                                return i;
                            }
                        }                        
                    }
                }              
                
            }

            return 0;
        }

        public int GetNodeFilter(dynamic dynJsonWorkflow)
        {
            int totalActionCount = 0;
            var nodeList = new List<dynamic>();

            var nodes = dynJsonWorkflow.SelectToken("nodes");
            if (nodes == null) return totalActionCount;

            foreach (var node in nodes)
            {
                var actionInfo = node.actionInfo;
                if (actionInfo == null)
                {
                    continue;
                }
                var checkchild = node.children;

                var groupActions = node.groupActions;
                if (groupActions != null)
                {
                    totalActionCount += groupActions.Count;
                }
                else if (checkchild != null)
                {
                    var childrenCount = node.children.Count;
                    totalActionCount = totalActionCount + childrenCount;
                }

                else
                {
                    nodeList.Add(actionInfo);
                    totalActionCount++;
                }
            }

            return totalActionCount;

        }

        public async Task<bool> SendMailAsync(string actionResultStatus)
        {
            try
            {
                _logger.Information($"Send mail Workflow Action Alert notification method started.");
                var AlertMessage = _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(_operationGroup.ReferenceId);
                var workflowOperationList = await _dataProvider.WorkflowOperation.GetWorkflowOperationById(_operationGroup.WorkflowOperationId);

              
                var mails = await GetAlertReceiverEmailIdsByInfraObjectId(_operationGroup.InfraObjectId);

                string type;
                string errorMessage;
                if (actionResultStatus == "Success")
                {
                    errorMessage = $@"</div>
                            <h2>CPAlert : Workflow Execution Alert </h2>
                            <div class='col-8'>
                                <p><strong>InfraObject Name:</strong> {_operationGroup.InfraObjectName}</p>  
                                <p><strong>Operational Service Name:</strong> {_operationGroup.BusinessServiceName}</p>                                         
                                <p><strong>Workflow Profile Name :</strong> {workflowOperationList.ProfileName}</p>    
                                <p><strong>Workflow Name :</strong> {_operationGroup.WorkflowName}</p>   
                                <p><strong>Workflow Action Name :</strong> {AlertMessage.Result.CurrentActionName}</p>                              
                                <p><strong>Status :</strong> {actionResultStatus}</p>                                 
                                <p><strong>Timestamp:</strong> {DateTime.Now}</p>
                            </div>";
                    type = "actionsuccess";
                }
                else
                {
                   
                    errorMessage = $@"</div>
                            <h2>CPAlert : Workflow Execution Alert </h2>
                            <div class='col-8'>
                                <p><strong>InfraObject Name:</strong> {_operationGroup.InfraObjectName}</p>  
                                <p><strong>Operational Service Name:</strong> {_operationGroup.BusinessServiceName}</p>                                         
                                 <p><strong>Workflow Profile Name :</strong> {workflowOperationList.ProfileName}</p>    
                                <p><strong>Workflow Name :</strong> {_operationGroup.WorkflowName}</p>   
                                <p><strong>Workflow Action Name :</strong> {AlertMessage.Result.CurrentActionName}</p>    
                                <p><strong>Status :</strong> {actionResultStatus}</p>  
                                <p><strong>ErrorMessage :</strong> {AlertMessage.Result.Message}</p>  
                                <p><strong>Timestamp:</strong> {DateTime.Now}</p>
                            </div>";

                    type = "actionfail";
                }
                var alert = new SendEmailCommand
                {
                    Type = type,
                    ErrorMessage = errorMessage,
                    Emails = mails
                };

                Mail mail = new(_logger, _dataProvider);
                await mail.SendMailToUsers(alert);

                return true;
            }
            catch (Exception exc)
            {
                _logger.Error($"Send mail Alert notification Success Scenario- 'false'. {exc.Message}");

                return false;
            }
        }

        public async Task<List<Email>> GetAlertReceiverEmailIdsByInfraObjectId(string infraObjectId)
        {
            var alertReceivers = new List<Email>();
            try
            {
                var message = await _dataProvider.AlertReceiver.GetAlertReceiver();
                message.ForEach(alertReceiver =>
                {
                    if(alertReceiver.IsActiveUser)
                    {
                        var email = new Email();

                        if (alertReceiver.Properties == null || string.IsNullOrWhiteSpace(alertReceiver.Properties)) return;
                        var alertReceiverProperties = JsonConvert.DeserializeObject<AssignedEntity>(alertReceiver.Properties)!;

                        var infraDtl = alertReceiverProperties.AssignedBusinessServices.SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
                            .SelectMany(assignedBusinessFunctions => assignedBusinessFunctions.AssignedInfraObjects).Any(assignedInfraObjects => assignedInfraObjects.Id.Equals(infraObjectId));

                        if (infraDtl)
                        {
                            email.ToMail = alertReceiver.EmailAddress;
                            email.Name = alertReceiver.Name;
                            alertReceivers.Add(email);
                        }
                    }
                    
                });
                return alertReceivers;
            }
            catch (Exception ex)
            {
                _logger.Error($"Error occurring while Get receiver mails in GetAlertReceiverEmailIdsByInfraObjectId method. error is : {ex.Message}");
                return alertReceivers;
            }
        }
        public async Task UpdateWorkflowOperationGroup(string actionName)
        {
            var workflowOperationGroup = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(_operationGroup.ReferenceId);
            workflowOperationGroup.Status = OperationStatus.RUNNING.ToTitleCase();
            workflowOperationGroup.NodeId = workflowOperationGroup.NodeId;
            workflowOperationGroup.NodeName = workflowOperationGroup.NodeName;
            workflowOperationGroup.CurrentActionName = actionName;
            await _dataProvider.WorkflowOperationGroup.UpdateWorkflowOperationGroup(workflowOperationGroup);
        }
        public async Task<bool> VerifyWorkflowAborted(bool abortStatus, int totalActionCount)
        {
            if (abortStatus)
            {
                _logger.Information("SequentialExecutor-ConditionalOperation 4 : Abort Button clicked " + abortStatus);
                await UpdateWorkflowOperationGroupInfra("Aborted", _operationGroup.ProgressStatus, _operationGroup.CurrentActionName, _operationGroup.CurrentActionId);
                _logger.Information("ABORT-Updating " + _operationGroup.ProgressStatus, _operationGroup.CurrentActionName, _operationGroup.CurrentActionId + " in UpdateWorkflowOperationgroupInfra");
                return true;
            }

            var operationGroups1 = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(_operationGroup.ReferenceId);

            if (operationGroups1.ConditionalOperation == (int)ConditionalOperation.ABORT || operationGroups1.IsAbort is 1)
            {
                await UpdateAbort(totalActionCount);

                await UpdateWorkflowOperationGroupInfra("Aborted", operationGroups1.ProgressStatus, operationGroups1.CurrentActionName, operationGroups1.CurrentActionId);

                _logger.Information("===========Parallel Workflow execution is aborted successfully. =========");

                return true;
            }

            return false;
        }
        public async Task UpdateAbort(int totalActionCount)
        {
            var workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(_operationGroup.ReferenceId);
            var actionResultId = workflowActionResults.FirstOrDefault(x => x.ConditionActionId == (int)ConditionalOperation.ABORT);
            if (actionResultId != null)
            {
                _logger.Information($"ABORT-ConditionalOperation: Abort Button clicked for ActionResultId: {actionResultId.ReferenceId}");

                await WorkflowActionResultStatusUpdate("Aborted", actionResultId.ReferenceId);

                _logger.Information($"ABORT-Status updated to 'Aborted' for ActionResultId: {actionResultId.ReferenceId}", actionResultId.ReferenceId);

                workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(_operationGroup.ReferenceId);

                var progressStatus = _operationGroup.ProgressStatus;

                if (workflowActionResults.Count > 0)
                {
                    var totalSuccessCount = workflowActionResults
                        .Count(x => new[] { "success", "skip", "aborted" }.Contains(x.Status.ToLower().Trim()));

                    progressStatus = $"{totalSuccessCount}/{totalActionCount}";
                    _logger.Information($"ABORT-Progress status updated: {progressStatus}");
                }
                await UpdateWorkflowOperationGroupInfra("Aborted", progressStatus, _operationGroup.CurrentActionName, _operationGroup.CurrentActionId);

                _logger.Information($"ABORT-Updated ProgressStatus and ActionDetails: {progressStatus}, ActionName: {_operationGroup.CurrentActionName}, ActionId: {_operationGroup.CurrentActionId}");
            }
        }
        public async Task WorkflowActionResultStatusUpdate(string status, string id)
        {
            try
            {
                _logger.Debug("Entered into SequentialExecutor-WorkflowActionResultStatusUpdate");

                var workflowResult = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultById(id);
                if (workflowResult == null)
                {
                    _logger.Error($"No WorkflowActionResult found with ID: {id}");
                    return; // Return early if no result found
                }

                string oldStatus = workflowResult.Status;
                workflowResult.Status = status;
                workflowResult.ConditionActionId = (int)ConditionalOperation.NONE;
                _logger.Debug($"Updating WorkflowActionResult with ID: {id}, Old Status: {oldStatus} New Status: {status}");
                // Update the workflow action result in the data provider
                await _dataProvider.WorkflowActionResult.UpdateWorkflowActionResult(workflowResult);
                // Log the successful update
                _logger.Debug($"SequentialExecutor-Updated WorkflowActionResult with ID: {id}, New Status: {status}");
            }
            catch (Exception exc)
            {
                _logger.Error("Exception occurring in SequentialExecutor-WorkflowActionResultStatusUpdate " + exc.Message);
            }
        }
        public async Task UpdateWorkflowOperationGroupInfra(string actionResultStatus, string progressStatus, string actionName, string wfActionId)
        {
            try
            {
                _logger.Debug($"Retrieved WorkflowOperationGroup with ReferenceId: {_operationGroup.ReferenceId}");

                var operationGroup = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(_operationGroup.ReferenceId);
                operationGroup.CurrentActionId = wfActionId;
                operationGroup.CurrentActionName = actionName;
                operationGroup.Status = actionResultStatus;
                operationGroup.ProgressStatus = progressStatus;
                operationGroup.ConditionalOperation = 0;
                await _dataProvider.WorkflowOperationGroup.UpdateWorkflowOperationGroup(operationGroup);

                _logger.Debug($"Successfully updated WorkflowOperationGroup: {operationGroup.ReferenceId}, Status: {operationGroup.Status}");
            }
            catch (Exception exc)
            {
                _logger.Exception("Exception in SequentialExecutor-UpdateWorkflowOperationGroupInfra.", exc);
                throw; // Optionally rethrow if this exception needs to be propagated
            }
        }
        public async Task UpdateWorkflowOperationGroupInfra2(string actionResultStatus, string progressStatus, string actionName, string wfActionId, int conditionId)
        {
            try
            {
                _logger.Information($"Retrieved WorkflowOperationGroup with ReferenceId: {_operationGroup.ReferenceId}");

                var operationGroup = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(_operationGroup.ReferenceId);
                operationGroup.CurrentActionId = wfActionId;
                operationGroup.CurrentActionName = actionName;
                operationGroup.Status = actionResultStatus;
                operationGroup.ProgressStatus = progressStatus;
                operationGroup.ConditionalOperation = conditionId;
                await _dataProvider.WorkflowOperationGroup.UpdateWorkflowOperationGroup(operationGroup);

                _logger.Information($"Successfully updated WorkflowOperationGroup: {operationGroup.ReferenceId}, Status: {operationGroup.Status}");
            }
            catch (Exception exc)
            {
                _logger.Exception("Exception in SequentialExecutor-UpdateWorkflowOperationGroupInfra.", exc);
                throw; // Optionally rethrow if this exception needs to be propagated
            }
        }
        public async Task UpdateWorkflowOperationGroupInfra1(string actionResultStatus, string progressStatus, string actionName, string wfActionId)
        {
            try
            {
                _logger.Information($"Retrieved WorkflowOperationGroup with ReferenceId: {_operationGroup.ReferenceId}");

                var operationGroup = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(_operationGroup.ReferenceId);
                operationGroup.CurrentActionId = wfActionId;
                operationGroup.CurrentActionName = actionName;
                operationGroup.Status = actionResultStatus;
                operationGroup.ProgressStatus = progressStatus;               
                await _dataProvider.WorkflowOperationGroup.UpdateWorkflowOperationGroup(operationGroup);

                _logger.Information($"Successfully updated WorkflowOperationGroup: {operationGroup.ReferenceId}, Status: {operationGroup.Status}");
            }
            catch (Exception exc)
            {
                _logger.Exception("Exception in SequentialExecutor-UpdateWorkflowOperationGroupInfra.", exc);
                throw; // Optionally rethrow if this exception needs to be propagated
            }
        }
        public bool IsSkipConditionOperation(dynamic actionResultStatus)
        {
            bool skipConditionOperation;

            string status = actionResultStatus.ToLower();

            if (status == ResultStatus.ERROR.ToLower())
            {
                _logger.Information($"Sequential Action Result failed. Waiting for ConditionAction: {actionResultStatus}");

                skipConditionOperation = false;
            }
            else
            {
                skipConditionOperation = true;
            }
            var actionMode = _operationGroup.ActionMode.ToLower();
            if (actionMode == ExecutionMode.STEP.ToLower())
            {
                _logger.Debug("WorkflowOperation is in Step Mode");

                if (status == ResultStatus.ERROR.ToLower() || status == ResultStatus.SUCCESS.ToLower())
                {
                    _logger.Debug("ActionResultStatus is either Error or Success. Skipping condition operation.");

                    skipConditionOperation = false;
                }
            }
            else if (actionMode == ExecutionMode.SIMULATE.ToLower())
            {
                _logger.Debug("WorkflowOperation is in Simulate Mode");
            }
            return skipConditionOperation;
        }
        public async Task<bool> HandleRetryCondition(List<WorkflowActionResult> workflowActionResults, int totalActionCount)
        {
            var actionResultId = workflowActionResults.FirstOrDefault(x => x.ConditionActionId == (int)ConditionalOperation.RETRY);

            if (actionResultId != null)
            {
                _logger.Debug($"SequentialAction-actionResultId for retry : {actionResultId}");

                await PerformRetry(actionResultId, workflowActionResults, totalActionCount);

                return true;
            }
            return false;
        }
        public async Task<dynamic> HandleReloadCondition(int totalActionCount, dynamic dynJsonWorkflow)
        {
            _logger.Information("SequentialAction-ConditionalOperation 3 : Reload Button clicked");

            if(_operationGroup.IsCustom)
            {
                var tempWorkflow = await _dataProvider.WorkflowExecutionTemp.GetWorkflowExecutionTempById(_operationGroup.WorkflowExecutionTempId);

                _logger.Information("RELOAD SequentialAction-Getting workflowId from tempWorkflow table  " + _operationGroup.WorkflowId);

                // Deserialize workflow properties into dynJsonWorkflow
                dynJsonWorkflow = JsonConvert.DeserializeObject(tempWorkflow.Properties)!;

            }
            else
            {
                var workflow = await _dataProvider.Workflow.GetWorkflowById(_operationGroup.WorkflowId);

                _logger.Information("RELOAD SequentialAction-Getting workflowId from Workflow table " + _operationGroup.WorkflowId);

                // Deserialize workflow properties into dynJsonWorkflow
                dynJsonWorkflow = JsonConvert.DeserializeObject(workflow.Properties)!;
            }
                

            // Get action results for the current workflow operation group
            var workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(_operationGroup.ReferenceId);

            var progressStatus = _operationGroup.ProgressStatus;

            if (workflowActionResults.Count > 0)
            {
                // Calculate success, skip, or aborted actions to update progress
                var totalSuccessCount = workflowActionResults
                    .Where(x => x.Status.ToLower().Trim() is "success" or
                                "skip" or
                                "aborted")
                    .ToList();

                progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";
                _logger.Information("Reload-Updating Progress status : " + progressStatus);
            }
            var LastActionResultStatus = workflowActionResults.FirstOrDefault();
            _logger.Information("LastActionResultStatus:" + LastActionResultStatus.Status);
            // Update the workflow operation group with the "Error" status
            await UpdateWorkflowOperationGroupInfra(LastActionResultStatus.Status, progressStatus, _operationGroup.CurrentActionName, _operationGroup.CurrentActionId);

            return dynJsonWorkflow; // Return the updated dynJsonWorkflow
        }
        public async Task<bool> HandleAbortCondition(int totalActionCount)
        {
            var operationGroups = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(_operationGroup.ReferenceId);

            if (operationGroups.ConditionalOperation == (int)ConditionalOperation.ABORT)
            {
                var workflowActionResult = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(_operationGroup.ReferenceId);
                var actionResultId = workflowActionResult.FirstOrDefault(x => x.ConditionActionId == (int)ConditionalOperation.ABORT);

                if (actionResultId != null)
                {
                    _logger.Information($"ABORT-ConditionalOperation 4 : Abort Button clicked for ActionResultId: {actionResultId.ReferenceId}");
                    await WorkflowActionResultStatusUpdate("Aborted", actionResultId.ReferenceId);
                    await UpdateProgressStatus(totalActionCount, "Aborted");
                    return true;
                }
            }
            return false;
        }
        private async Task UpdateProgressStatus(int totalActionCount, string status)
        {
            var workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(_operationGroup.ReferenceId);

            var totalSuccessCount = workflowActionResults
                .Where(x => x.Status.Equals("success", StringComparison.OrdinalIgnoreCase)
                            || x.Status.Equals("skip", StringComparison.OrdinalIgnoreCase)
                            || x.Status.Equals("aborted", StringComparison.OrdinalIgnoreCase))
                .ToList();

            var progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";
            _logger.Information($"{status}-Updating Progress status : {progressStatus}");

            await UpdateWorkflowOperationGroupInfra(status, progressStatus, _operationGroup.CurrentActionName, _operationGroup.CurrentActionId);
        }
        private async Task PerformRetry(WorkflowActionResult actionResultId, List<WorkflowActionResult> workflowActionResults, int totalActionCount)
        {
            try
            {
                _logger.Information($"Retry triggered for ActionResultId: {actionResultId.ReferenceId}");

                // Update the status of the action result to "Retry"
                await WorkflowActionResultStatusUpdate("Retry", actionResultId.ReferenceId);
                _logger.Information(@$"Updated status to 'Retry' for ActionResultId: {actionResultId.ReferenceId}");

                // Delete the action result from the database
                _logger.Information($"Deleting ActionResultId: {actionResultId.ReferenceId} from WorkflowActionResult table");
                await _dataProvider.WorkflowActionResult.DeleteWorkflowActionResultById(actionResultId.ReferenceId);
                _logger.Information($"Deleted ActionResultId: {actionResultId.ReferenceId} from WorkflowActionResult table");

                // Fetch the updated list of action results
                workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(_operationGroup.ReferenceId);

                // Recalculate progress status
                string progressStatus = CalculateProgressStatus(workflowActionResults, totalActionCount);

                // Log progress status update
                _logger.Information($"Progress status updated to: {progressStatus}");

                // Update the workflow operation group with the new status
                await UpdateWorkflowOperationGroupInfra("Running", progressStatus, _operationGroup.CurrentActionName, _operationGroup.CurrentActionId);
                _logger.Information($"Updated workflow operation group with status 'Running' and progress: {progressStatus}");
            }
            catch (Exception exc)
            {
                _logger.Error($"Error during retry operation for ActionResultId: {actionResultId.ReferenceId}. Exception: {exc.Message}");
            }
        }
        private string CalculateProgressStatus(List<WorkflowActionResult> workflowActionResults, int totalActionCount)
        {
            var totalSuccessCount = workflowActionResults
                .Count(x => new[] { "success", "skip", "aborted" }.Contains(x.Status.ToLower().Trim()));

            return $"{totalSuccessCount}/{totalActionCount}";
        } 
        public async Task<bool> WaitForCondition(int i, bool resultStatus, string actionResultStatus, WorkflowOperationGroup operationGroups, List<WorkflowActionResult> workflowActionResults, int totalActionCount)
        {
            try
            {
                if (operationGroups.ConditionalOperation == (int)ConditionalOperation.NEXT)
                {
                    var actionResult = workflowActionResults.FirstOrDefault(x => (x.ConditionActionId == (int)ConditionalOperation.NEXT));

                    if (actionResult != null)
                    {
                        resultStatus = true;

                        string statusToUpdate = actionResult.Status.Equals(ResultStatus.SUCCESS.ToString(), StringComparison.OrdinalIgnoreCase)
                            ? ResultStatus.SUCCESS.ToTitleCase()
                            : ResultStatus.SKIP.ToTitleCase();

                        await WorkflowActionResultStatusUpdate(statusToUpdate, actionResult.ReferenceId);

                        var actionResult1 = workflowActionResults.FirstOrDefault(x => (x.ConditionActionId == (int)ConditionalOperation.NEXT));

                        var statusMessage = actionResult1.Status.Equals(ResultStatus.SUCCESS.ToString(), StringComparison.OrdinalIgnoreCase)
                            ? "Success"
                            : "Skip";

                        _logger.Debug($"SequentialExecutor - Updating {statusMessage} status and action resultId {actionResult.ReferenceId} from WorkflowActionResultStatusUpdate");

                        workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(operationGroups.ReferenceId);
                        var totalSuccessCount = workflowActionResults.Where(x => new[] { "success", "skip", "bypassed" }.Contains(x.Status.ToLower().Trim())).ToList();

                        string progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";

                        if (totalSuccessCount.Count == totalActionCount)
                        {
                            _logger.Information($"NEXT - totalSuccessCount.count ({totalSuccessCount.Count}) equals totalActionCount ({totalActionCount})");
                            await UpdateWorkflowOperationGroupInfra("Completed", progressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId);
                            _logger.Information($"Update Completed {progressStatus} in UpdateWorkflowOperationGroupInfra");
                            return resultStatus;
                        }
                        await UpdateWorkflowOperationGroupInfra(OperationStatus.RUNNING.ToTitleCase(), progressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId);
                    }

                }
                if (operationGroups.ConditionalOperation == (int)ConditionalOperation.ABORT)
                {

                    var workflowActionResult = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(operationGroups.ReferenceId);
                    var actionResultId = workflowActionResult.FirstOrDefault(x => x.ConditionActionId == (int)ConditionalOperation.ABORT);

                    if (actionResultId != null)
                    {
                        _logger.Information($"ABORT - ConditionalOperation 4 : Abort Button clicked for ActionResultId: {actionResultId.ReferenceId}");
                        await WorkflowActionResultStatusUpdate("Aborted", actionResultId.ReferenceId);
                        _logger.Information($"ABORT - Updating status Abort and actionResultId: {actionResultId.ReferenceId}");

                        workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(operationGroups.ReferenceId);
                        var totalSuccessCount = workflowActionResults
                            .Where(x => new[] { "success", "skip", "aborted" }.Contains(x.Status.ToLower().Trim()))
                            .ToList();

                        string progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";
                        _logger.Information($"ABORT - Updating Progress status: {progressStatus}");

                        await UpdateWorkflowOperationGroupInfra2("Aborted", progressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId,4);

                        _logger.Information($"ABORT - Updating {progressStatus} in UpdateWorkflowOperationGroupInfra");

                        resultStatus = true;
                    }
                }








            }
            catch (Exception exc)
            {
                _logger.Error("Exception occurring in SequentialExecutor-WaitForCondition " + exc.Message);
            }
            return resultStatus;
        }

        #region New Wait for conditions 
        public async Task<bool> WaitForConditionNew(int i, bool resultStatus, string actionResultStatus, WorkflowOperationGroup operationGroups, List<WorkflowActionResult> workflowActionResults,
            int totalActionCount, dynamic dynJsonWorkflow, IDictionary<string, dynamic> nodeList, InterpreterManagerModule interpreterManager, IServiceProvider serviceProvider)
        {

            var conditionActionPairs = workflowActionResults
          .Select(x => new { x.ConditionActionId, x.WorkflowActionName })
          .ToList();
            _logger.Debug("ConditionAction details: {Details}",
                string.Join(", ", conditionActionPairs.Select(p => $"[ActionName: {p.WorkflowActionName}, ConditionId: {p.ConditionActionId}]")));

            try
            {
               // _logger.Information("operationGroups ConditionalOperation Id:" + operationGroups.ConditionalOperation + "");

                switch (operationGroups.ConditionalOperation)
                {
                    case (int)ConditionalOperation.NEXT:
                        _logger.Information("Handling NEXT operation...");
                        var actionResult = workflowActionResults.FirstOrDefault(x => (x.ConditionActionId == (int)ConditionalOperation.NEXT));

                        if (actionResult != null)
                        {
                            //resultStatus = true;

                            string statusToUpdate = actionResult.Status.Equals(ResultStatus.SUCCESS.ToString(), StringComparison.OrdinalIgnoreCase)
                                ? ResultStatus.SUCCESS.ToTitleCase()
                                : ResultStatus.SKIP.ToTitleCase();

                            await WorkflowActionResultStatusUpdate(statusToUpdate, actionResult.ReferenceId);

                            var actionResult1 = workflowActionResults.FirstOrDefault(x => (x.ConditionActionId == (int)ConditionalOperation.NEXT));

                            var statusMessage = actionResult1!.Status.Equals(ResultStatus.SUCCESS.ToString(), StringComparison.OrdinalIgnoreCase)
                                ? "Success"
                                : "Skip";

                            _logger.Debug($"SequentialExecutor - Updating {statusMessage} status and action resultId {actionResult.ReferenceId} from WorkflowActionResultStatusUpdate");

                            workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(operationGroups.ReferenceId);
                            var totalSuccessCount = workflowActionResults.Where(x => new[] { "success", "skip", "bypassed" }.Contains(x.Status.ToLower().Trim())).ToList();

                            string progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";

                            if (totalSuccessCount.Count == totalActionCount)
                            {
                                _logger.Information($"NEXT - totalSuccessCount.count ({totalSuccessCount.Count}) equals totalActionCount ({totalActionCount})");
                                await UpdateWorkflowOperationGroupInfra("Completed", progressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId);
                                _logger.Information($"Update Completed {progressStatus} in UpdateWorkflowOperationGroupInfra");
                                return resultStatus;
                            }
                            await UpdateWorkflowOperationGroupInfra(OperationStatus.RUNNING.ToTitleCase(), progressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId);
                        }
                        break;

                    case (int)ConditionalOperation.ABORT:
                        _logger.Information("Handling ABORT operation...");
                        var workflowActionResult = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(operationGroups.ReferenceId);
                        var actionResultId = workflowActionResult.FirstOrDefault(x => x.ConditionActionId == (int)ConditionalOperation.ABORT);

                        if (actionResultId != null)
                        {
                            _logger.Information($"ABORT - ConditionalOperation 4 : Abort Button clicked for ActionResultId: {actionResultId.ReferenceId}");
                            await WorkflowActionResultStatusUpdate("Aborted", actionResultId.ReferenceId);
                            _logger.Information($"ABORT - Updating status Abort and actionResultId: {actionResultId.ReferenceId}");

                            workflowActionResults = await _dataProvider.WorkflowActionResult.GetWorkflowActionResultByWorkflowOperationGroupId(operationGroups.ReferenceId);
                            var totalSuccessCount = workflowActionResults
                                .Where(x => new[] { "success", "skip", "aborted" }.Contains(x.Status.ToLower().Trim()))
                                .ToList();

                            string progressStatus = $"{totalSuccessCount.Count}/{totalActionCount}";
                            _logger.Information($"ABORT - Updating Progress status: {progressStatus}");

                            await UpdateWorkflowOperationGroupInfra2("Aborted", progressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId, 4);

                            _logger.Information($"ABORT - Updating {progressStatus} in UpdateWorkflowOperationGroupInfra");

                            resultStatus = true;
                        }
                        break;

                       case(int)ConditionalOperation.RELOAD:

                        _logger.Information("Handling RELOAD operation...");

                        if (operationGroups.IsCustom)
                        {
                            var tempWorkflow = await _dataProvider.WorkflowExecutionTemp.GetWorkflowExecutionTempById(operationGroups.WorkflowExecutionTempId);

                            _logger.Information("RELOAD SequentialAction-Getting workflowId from tempWorkflow table  " + operationGroups.WorkflowId);

                            // Deserialize workflow properties into dynJsonWorkflow
                            dynJsonWorkflow = JsonConvert.DeserializeObject(tempWorkflow.Properties)!;

                        }
                        else
                        {
                            var workflow = await _dataProvider.Workflow.GetWorkflowById(operationGroups.WorkflowId);

                            _logger.Information("RELOAD SequentialAction-Getting workflowId from GetWorkflowById " + operationGroups.WorkflowId);

                            dynJsonWorkflow = JsonConvert.DeserializeObject(workflow.Properties)!;

                        }
                        var checkchild2 = dynJsonWorkflow?.SelectToken("nodes" + "[" + i + "]" + ".children");
                        nodeList.Clear();
                        _logger.Debug("RunParallelWorkflow-checkchild: " + checkchild2);
                        for (int j = 0; j < dynJsonWorkflow?.SelectToken("nodes" + "[" + i + "]" + ".children").Count; j++)
                        {

                            var getChildNode = dynJsonWorkflow.SelectToken("nodes" + "[" + i + "]" + ".children")[j];
                            var test = getChildNode.SelectToken("actionInfo");
                            var stepId = getChildNode.SelectToken("stepId").Value;
                            nodeList.Add(stepId, test);
                        }
                        resultStatus = true;
                        _logger.Information("ConditionalOperation 3 : Reload Success");
                        break;

                    case (int)ConditionalOperation.RETRY:


                        _logger.Information("Handling RETRY operation...");

                        var actionsToRetry = workflowActionResults
                            .Where(x => x.ConditionActionId == 2 && x.Status == "Error")
                            .ToList();

                        if (!actionsToRetry.Any()) resultStatus = false;

                        ThreadPool.SetMinThreads(1000, 1000);
                        _logger.Information("ProcessorCount: {ProcessorCount}, ActionsToRetry: {RetryCount}", Environment.ProcessorCount,
                            actionsToRetry.Count);

                        int maxParallelism = Math.Min(Environment.ProcessorCount * 2, actionsToRetry.Count);
                        _logger.Information("maxParallelism Count :" + maxParallelism);
                        await Parallel.ForEachAsync(actionsToRetry, new ParallelOptions
                        {
                            MaxDegreeOfParallelism = maxParallelism
                        }, async (action, token) =>
                        {
                            try
                            {
                                operationGroups = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(action.WorkflowOperationGroupId);
                                _logger.Information($"Retrying GetWorkflowOperationGroups [ID:{action.ReferenceId}, Name:{action.WorkflowActionName}, Step:{action.StepId}]");
                                await WorkflowActionResultStatusUpdate("Retry", action.ReferenceId);
                                await _dataProvider.WorkflowActionResult.DeleteWorkflowActionResultById(action.ReferenceId);
                                _logger.Information("RETRY-Deleting actionResultId from DeleteWorkflowActionResultById: " + action.ReferenceId);
                                await UpdateWorkflowOperationGroupInfra("Running", operationGroups.ProgressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId);
                                _logger.Information("Update Running " + operationGroups.ProgressStatus, operationGroups.CurrentActionName, operationGroups.CurrentActionId + " in UpdateWorkflowOperationgroupInfra");

                                await UpdateWorkflowOperationGroupInfra(
                                  "Running",
                                  operationGroups.ProgressStatus,
                                  operationGroups.CurrentActionName,
                                  operationGroups.CurrentActionId);

                                try
                                {
                                    await _semaphore.WaitAsync();
                                    var parallelAction = new ParallelAction(_operationGroup, _logger,
                                        _dataProvider, serviceProvider);

                                    actionResultStatus = await parallelAction.RunParallelAction(
                                       interpreterManager,
                                       totalActionCount,
                                       nodeList[action.StepId],
                                       action.StepId,
                                       true);

                                    _logger.Information($"Retry completed with status: {actionResultStatus}");
                                }
                                finally
                                {
                                    _semaphore.Release();
                                }
                                _logger.Information("Retry result for action {ActionId}: {Status}", action.ReferenceId, actionResultStatus);

                            }
                            catch (Exception ex)
                            {

                                _logger.Error(ex, "Retry failed for Action [ID:{ActionId}, Step:{StepId}]", action.ReferenceId, action.StepId);
                            }
                        });

                        break;

                    default:
                        _logger.Warning("Unknown ConditionalOperation: {Operation}", operationGroups.ConditionalOperation);
                        break;


                }

            }
            catch (Exception exc)
            {
                _logger.Error("Exception occurring in SequentialExecutor-WaitForCondition " + exc.Message);
            }
            return resultStatus;
        }
        #endregion


        #region Terminate Workflow conditions


        #endregion
        public async Task CheckThreadState(CancellationTokenSource cancellationTokenSource)
        {
            try
            {
                if (cancellationTokenSource.Token.IsCancellationRequested)
                {
                    _logger.Debug("CheckThreadState cancellation requested:" + cancellationTokenSource.Token.IsCancellationRequested);

                    cancellationTokenSource.Cancel();
                    cancellationTokenSource.Dispose();

                    await UpdateWorkflowOperationGroupPauseStatus("Paused", _operationGroup.ProgressStatus, _operationGroup.CurrentActionName, _operationGroup.CurrentActionId, 2);
                    throw new ContinuityPatrolException(ExceptionType.PauseStopExecution, "Manually Pause the workflow to stop the executed actions.");
                }
            }
            catch (Exception e)
            {
                _logger.Error("CheckThreadState throws error:" + e.Message);
                throw;
            }
        }
        public async Task UpdateWorkflowOperationGroupPauseStatus(string actionResultStatus, string progressStatus, string actionName, string wfActionId, int isPause)
        {
            try
            {
                _logger.Debug("Entered into SequentialExecutor-UpdateWorkflowOperationGroupPauseStatus");

                // Retrieve the operation group
                var operationGroup = await _dataProvider.WorkflowOperationGroup.GetWorkflowOperationGroupById(_operationGroup.ReferenceId);
                _logger.Debug("SequentialExecutor-Retrieving WorkflowOperationGroup for ReferenceId: " + _operationGroup.ReferenceId);

                // Update operation group details
                operationGroup.CurrentActionId = wfActionId;
                operationGroup.CurrentActionName = actionName;
                operationGroup.Status = actionResultStatus;
                operationGroup.ProgressStatus = progressStatus;
                operationGroup.IsPause = isPause;

                // Check if NodeId needs updating (remove if not necessary)
                // operationGroup.NodeId = newNodeId; // Ensure this is the correct logic if NodeId is being modified

                // Update the operation group in the database
                await _dataProvider.WorkflowOperationGroup.UpdateWorkflowOperationGroup(operationGroup);

                _logger.Information("SequentialExecutor-Updated WorkflowOperationGroup with current action details: " + operationGroup);
            }
            catch (Exception exc)
            {
                _logger.Error("Exception in SequentialExecutor-UpdateWorkflowOperationGroupPauseStatus: " + exc.Message + "\n" + exc.StackTrace);
            }

        }

        

    }
}
